import type { HeadersObject } from "./types";
export declare function rawHeaders(headers: HeadersObject): never[];
type Fn = (...args: any[]) => any;
export declare function mergeFns(...functions: Fn[]): (...args: any[]) => void;
export declare function createNotImplementedError(name: string): Error;
export declare function notImplemented<Fn extends (...args: any) => any>(name: string): Fn;
export interface Promisifiable {
    (): any;
    native: Promisifiable;
    __promisify__: () => Promise<any>;
}
export declare function notImplementedAsync(name: string): Promisifiable;
export declare function notImplementedClass<T = unknown>(name: string): T;
export {};
