import { notImplemented } from "../../_internal/utils.mjs";
import { TLSSocket } from "./internal/tls-socket.mjs";
import { Server } from "./internal/server.mjs";
import { SecureContext } from "./internal/secure-context.mjs";
import * as constants from "./internal/constants.mjs";
export * from "./internal/constants.mjs";
export { TLSSocket } from "./internal/tls-socket.mjs";
export { Server } from "./internal/server.mjs";
export { SecureContext } from "./internal/secure-context.mjs";
export const connect = function connect2() {
  return new TLSSocket();
};
export const createServer = function createServer2() {
  return new Server();
};
export const checkServerIdentity = notImplemented("tls.checkServerIdentity");
export const convertALPNProtocols = notImplemented("tls.convertALPNProtocols");
export const createSecureContext = notImplemented("tls.createSecureContext");
export const createSecurePair = notImplemented(
  "tls.createSecurePair"
);
export const getCiphers = notImplemented("tls.getCiphers");
export const rootCertificates = [];
export default {
  ...constants,
  SecureContext,
  Server,
  TLSSocket,
  checkServerIdentity,
  connect,
  convertALPNProtocols,
  createSecureContext,
  createSecurePair,
  createServer,
  getCiphers,
  rootCertificates
};
