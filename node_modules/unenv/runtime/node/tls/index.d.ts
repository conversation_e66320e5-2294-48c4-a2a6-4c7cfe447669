import type tls from "node:tls";
export * from "./internal/constants";
export { TLSSocket } from "./internal/tls-socket";
export { Server } from "./internal/server";
export { SecureContext } from "./internal/secure-context";
export declare const connect: typeof tls.connect;
export declare const createServer: typeof tls.createServer;
export declare const checkServerIdentity: typeof tls.checkServerIdentity;
export declare const convertALPNProtocols: (...args: any) => any;
export declare const createSecureContext: typeof tls.createSecureContext;
export declare const createSecurePair: typeof tls.createSecurePair;
export declare const getCiphers: typeof tls.getCiphers;
export declare const rootCertificates: typeof tls.rootCertificates;
declare const _default: typeof tls;
export default _default;
