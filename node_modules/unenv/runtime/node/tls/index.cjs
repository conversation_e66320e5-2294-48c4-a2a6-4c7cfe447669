"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  connect: true,
  createServer: true,
  checkServerIdentity: true,
  convertALPNProtocols: true,
  createSecureContext: true,
  createSecurePair: true,
  getCiphers: true,
  rootCertificates: true,
  TLSSocket: true,
  Server: true,
  SecureContext: true
};
Object.defineProperty(exports, "SecureContext", {
  enumerable: true,
  get: function () {
    return _secureContext.SecureContext;
  }
});
Object.defineProperty(exports, "Server", {
  enumerable: true,
  get: function () {
    return _server.Server;
  }
});
Object.defineProperty(exports, "TLSSocket", {
  enumerable: true,
  get: function () {
    return _tlsSocket.TLSSocket;
  }
});
exports.rootCertificates = exports.getCiphers = exports.default = exports.createServer = exports.createSecurePair = exports.createSecureContext = exports.convertALPNProtocols = exports.connect = exports.checkServerIdentity = void 0;
var _utils = require("../../_internal/utils.cjs");
var _tlsSocket = require("./internal/tls-socket.cjs");
var _server = require("./internal/server.cjs");
var _secureContext = require("./internal/secure-context.cjs");
var constants = _interopRequireWildcard(require("./internal/constants.cjs"));
Object.keys(constants).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === constants[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return constants[key];
    }
  });
});
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
const connect = exports.connect = function connect2() {
  return new _tlsSocket.TLSSocket();
};
const createServer = exports.createServer = function createServer2() {
  return new _server.Server();
};
const checkServerIdentity = exports.checkServerIdentity = (0, _utils.notImplemented)("tls.checkServerIdentity");
const convertALPNProtocols = exports.convertALPNProtocols = (0, _utils.notImplemented)("tls.convertALPNProtocols");
const createSecureContext = exports.createSecureContext = (0, _utils.notImplemented)("tls.createSecureContext");
const createSecurePair = exports.createSecurePair = (0, _utils.notImplemented)("tls.createSecurePair");
const getCiphers = exports.getCiphers = (0, _utils.notImplemented)("tls.getCiphers");
const rootCertificates = exports.rootCertificates = [];
module.exports = {
  ...constants,
  SecureContext: _secureContext.SecureContext,
  Server: _server.Server,
  TLSSocket: _tlsSocket.TLSSocket,
  checkServerIdentity,
  connect,
  convertALPNProtocols,
  createSecureContext,
  createSecurePair,
  createServer,
  getCiphers,
  rootCertificates
};