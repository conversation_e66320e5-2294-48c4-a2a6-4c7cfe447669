import { notImplemented } from "../../_internal/utils.mjs";
import * as _path from "pathe";
export * from "pathe";
const _pathModule = {
  ..._path,
  platform: "posix",
  posix: void 0,
  win32: void 0,
  _makeLong: (path) => path,
  // https://github.com/unjs/pathe/issues/182
  matchesGlob: notImplemented(`path.matchesGlob`)
};
_pathModule.posix = _pathModule;
_pathModule.win32 = _pathModule;
export const posix = _pathModule;
export const win32 = _pathModule;
export const platform = "posix";
export const _makeLong = _pathModule._makeLong;
export const matchesGlob = _pathModule.matchesGlob;
export default _pathModule;
