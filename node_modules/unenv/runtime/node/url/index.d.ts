import type nodeUrl from "node:url";
import * as querystring from "node:querystring";
import { fileURLToPath, urlToHttpOptions } from "./internal/url";
declare class Url implements nodeUrl.Url {
    auth: string | null;
    hash: string | null;
    host: string | null;
    hostname: string | null;
    href: string;
    path: string | null;
    pathname: string | null;
    protocol: string | null;
    search: string | null;
    slashes: boolean | null;
    port: string | null;
    query: string | querystring.ParsedUrlQuery | null;
    parse(url: string, parseQueryString?: boolean, slashesDenoteHost?: boolean): this;
    format(): string;
    resolve(relative: string): string;
    resolveObject(relative: nodeUrl.Url): Url;
    parseHost(): void;
}
declare function urlParse(url: string | Url, parseQueryString?: boolean, slashesDenoteHost?: boolean): Url;
declare function urlFormat(urlObject: string | Url, options: nodeUrl.URLFormatOptions): any;
declare function urlResolve(source: string, relative: string): string;
declare function urlResolveObject(source: string, relative: nodeUrl.Url): any;
declare function pathToFileURL(path: string, options?: {
    windows?: boolean;
}): URL;
declare const URL: {
    new (url: string | URL, base?: string | URL): URL;
    prototype: URL;
    canParse(url: string | URL, base?: string | URL): boolean;
    createObjectURL(obj: Blob | MediaSource): string;
    parse(url: string | URL, base?: string | URL): URL | null;
    revokeObjectURL(url: string): void;
};
declare const URLSearchParams: {
    new (init?: string[][] | Record<string, string> | string | URLSearchParams): URLSearchParams;
    prototype: URLSearchParams;
};
declare const domainToASCII: (input: string) => string;
declare const domainToUnicode: (input: string) => string;
export { Url, urlParse as parse, urlResolve as resolve, urlResolveObject as resolveObject, urlFormat as format, URL, URLSearchParams, domainToASCII, domainToUnicode, pathToFileURL, fileURLToPath, // eslint-disable-line unicorn/prefer-export-from
urlToHttpOptions, };
declare const _default: {
    Url: typeof Url;
    parse: typeof urlParse;
    resolve: typeof urlResolve;
    resolveObject: typeof urlResolveObject;
    format: typeof urlFormat;
    URL: {
        new (url: string | URL, base?: string | URL): URL;
        prototype: URL;
        canParse(url: string | URL, base?: string | URL): boolean;
        createObjectURL(obj: Blob | MediaSource): string;
        parse(url: string | URL, base?: string | URL): URL | null;
        revokeObjectURL(url: string): void;
    };
    URLSearchParams: {
        new (init?: string[][] | Record<string, string> | string | URLSearchParams): URLSearchParams;
        prototype: URLSearchParams;
    };
    domainToASCII: (input: string) => string;
    domainToUnicode: (input: string) => string;
    pathToFileURL: typeof pathToFileURL;
    fileURLToPath: typeof fileURLToPath;
    urlToHttpOptions: typeof urlToHttpOptions;
};
export default _default;
