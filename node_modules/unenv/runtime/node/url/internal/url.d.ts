export declare const unsafeProtocol: Set<string>;
export declare const hostlessProtocol: Set<string>;
export declare const slashedProtocol: Set<string>;
export declare function pathToFileURL(filepath: string, options?: {
    windows?: boolean;
}): URL;
export declare function fileURLToPath(path: string | URL, options?: {
    windows?: boolean;
}): any;
/**
 * Utility function that converts a URL object into an ordinary options object
 * as expected by the `http.request` and `https.request` APIs.
 * @param {URL} url
 * @returns {Record<string, unknown>}
 */
export declare function urlToHttpOptions(url: URL): {
    protocol: string;
    hostname: any;
    hash: string;
    search: string;
    pathname: string;
    path: string;
    href: string;
    port: number | undefined;
    auth: string | undefined;
    host: string;
    toString(): string;
    origin: string;
    password: string;
    searchParams: URLSearchParams;
    username: string;
    toJSON(): string;
    __proto__: null;
};
