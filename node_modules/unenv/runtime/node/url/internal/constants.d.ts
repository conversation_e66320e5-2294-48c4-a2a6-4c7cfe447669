export declare const CHAR_UPPERCASE_A = 65;
export declare const CHAR_LOWERCASE_A = 97;
export declare const CHAR_UPPERCASE_Z = 90;
export declare const CHAR_LOWERCASE_Z = 122;
export declare const CHAR_UPPERCASE_C = 67;
export declare const CHAR_LOWERCASE_B = 98;
export declare const CHAR_LOWERCASE_E = 101;
export declare const CHAR_LOWERCASE_N = 110;
export declare const CHAR_DOT = 46;
export declare const CHAR_FORWARD_SLASH = 47;
export declare const CHAR_BACKWARD_SLASH = 92;
export declare const CHAR_VERTICAL_LINE = 124;
export declare const CHAR_COLON = 58;
export declare const CHAR_QUESTION_MARK = 63;
export declare const CHAR_UNDERSCORE = 95;
export declare const CHAR_LINE_FEED = 10;
export declare const CHAR_CARRIAGE_RETURN = 13;
export declare const CHAR_TAB = 9;
export declare const CHAR_FORM_FEED = 12;
export declare const CHAR_EXCLAMATION_MARK = 33;
export declare const CHAR_HASH = 35;
export declare const CHAR_SPACE = 32;
export declare const CHAR_NO_BREAK_SPACE = 160;
export declare const CHAR_ZERO_WIDTH_NOBREAK_SPACE = 65279;
export declare const CHAR_LEFT_SQUARE_BRACKET = 91;
export declare const CHAR_RIGHT_SQUARE_BRACKET = 93;
export declare const CHAR_LEFT_ANGLE_BRACKET = 60;
export declare const CHAR_RIGHT_ANGLE_BRACKET = 62;
export declare const CHAR_LEFT_CURLY_BRACKET = 123;
export declare const CHAR_RIGHT_CURLY_BRACKET = 125;
export declare const CHAR_HYPHEN_MINUS = 45;
export declare const CHAR_PLUS = 43;
export declare const CHAR_DOUBLE_QUOTE = 34;
export declare const CHAR_SINGLE_QUOTE = 39;
export declare const CHAR_PERCENT = 37;
export declare const CHAR_SEMICOLON = 59;
export declare const CHAR_CIRCUMFLEX_ACCENT = 94;
export declare const CHAR_GRAVE_ACCENT = 96;
export declare const CHAR_AT = 64;
export declare const CHAR_AMPERSAND = 38;
export declare const CHAR_EQUAL = 61;
export declare const CHAR_0 = 48;
export declare const CHAR_9 = 57;
