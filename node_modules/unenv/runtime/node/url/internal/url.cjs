"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.fileURLToPath = fileURLToPath;
exports.hostlessProtocol = void 0;
exports.pathToFileURL = pathToFileURL;
exports.unsafeProtocol = exports.slashedProtocol = void 0;
exports.urlToHttpOptions = urlToHttpOptions;
var _nodePath = _interopRequireDefault(require("node:path"));
var punnycode = _interopRequireWildcard(require("../../punycode/index.cjs"));
var _constants = require("./constants.cjs");
var _errors = require("./errors.cjs");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const unsafeProtocol = exports.unsafeProtocol = /* @__PURE__ */new Set(["javascript", "javascript:"]);
const hostlessProtocol = exports.hostlessProtocol = /* @__PURE__ */new Set(["javascript", "javascript:"]);
const slashedProtocol = exports.slashedProtocol = /* @__PURE__ */new Set(["http", "http:", "https", "https:", "ftp", "ftp:", "gopher", "gopher:", "file", "file:", "ws", "ws:", "wss", "wss:"]);
const FORWARD_SLASH = /\//g;
function pathToFileURL(filepath, options) {
  const windows = options?.windows;
  if (windows && String.prototype.startsWith.call(filepath, "\\\\")) {
    const outURL = new URL("file://");
    const isExtendedUNC = String.prototype.startsWith.call(filepath, "\\\\?\\UNC\\");
    const prefixLength = isExtendedUNC ? 8 : 2;
    const hostnameEndIndex = String.prototype.indexOf.call(filepath, "\\", prefixLength);
    if (hostnameEndIndex === -1) {
      throw new _errors.ERR_INVALID_ARG_VALUE("path", filepath, "Missing UNC resource path");
    }
    const hostname = String.prototype.slice.call(filepath, prefixLength, hostnameEndIndex);
    outURL.hostname = punnycode.toASCII(hostname);
    outURL.pathname = encodePathChars(filepath.slice(hostnameEndIndex).replace(backslashRegEx, "/"), {
      windows
    });
    return outURL;
  }
  let resolved = windows ? _nodePath.default.win32.resolve(filepath) : _nodePath.default.posix.resolve(filepath);
  const filePathLast = String.prototype.charCodeAt.call(filepath, filepath.length - 1);
  if ((filePathLast === _constants.CHAR_FORWARD_SLASH || windows && filePathLast === _constants.CHAR_BACKWARD_SLASH) && resolved.at(-1) !== _nodePath.default.sep) resolved += "/";
  resolved = encodePathChars(resolved, {
    windows
  });
  if (String.prototype.indexOf.call(resolved, "?") !== -1) resolved = resolved.replace(questionRegex, "%3F");
  if (String.prototype.indexOf.call(resolved, "#") !== -1) resolved = resolved.replace(hashRegex, "%23");
  return new URL(`file://${resolved}`);
}
function fileURLToPath(path2, options) {
  const windows = options?.windows;
  if (typeof path2 === "string") path2 = new URL(path2);else if (!isURL(path2)) throw new _errors.ERR_INVALID_ARG_TYPE("path", ["string", "URL"], path2);
  if (path2.protocol !== "file:") throw new _errors.ERR_INVALID_URL_SCHEME("file");
  return windows ? getPathFromURLWin32(path2) : getPathFromURLPosix(path2);
}
function urlToHttpOptions(url) {
  const {
    hostname,
    pathname,
    port,
    username,
    password,
    search
  } = url;
  return {
    __proto__: null,
    ...url,
    // In case the url object was extended by the user.
    protocol: url.protocol,
    hostname: hostname && String.prototype.startsWith.call(hostname, "[") ? String.prototype.slice.call(hostname, 1, -1) : hostname,
    hash: url.hash,
    search,
    pathname,
    path: `${pathname || ""}${search || ""}`,
    href: url.href,
    port: port === "" ? void 0 : Number(port),
    auth: username || password ? `${decodeURIComponent(username)}:${decodeURIComponent(password)}` : void 0
  };
}
const percentRegEx = /%/g;
const backslashRegEx = /\\/g;
const newlineRegEx = /\n/g;
const carriageReturnRegEx = /\r/g;
const tabRegEx = /\t/g;
const questionRegex = /\?/g;
const hashRegex = /#/g;
function encodePathChars(filepath, options) {
  const windows = options?.windows;
  if (String.prototype.indexOf.call(filepath, "%") !== -1) filepath = filepath.replace(percentRegEx, "%25");
  if (!windows && String.prototype.indexOf.call(filepath, "\\") !== -1) filepath = filepath.replace(backslashRegEx, "%5C");
  if (String.prototype.indexOf.call(filepath, "\n") !== -1) filepath = filepath.replace(newlineRegEx, "%0A");
  if (String.prototype.indexOf.call(filepath, "\r") !== -1) filepath = filepath.replace(carriageReturnRegEx, "%0D");
  if (String.prototype.indexOf.call(filepath, "	") !== -1) filepath = filepath.replace(tabRegEx, "%09");
  return filepath;
}
function getPathFromURLWin32(url) {
  const hostname = url.hostname;
  let pathname = url.pathname;
  for (let n = 0; n < pathname.length; n++) {
    if (pathname[n] === "%") {
      const third = pathname.codePointAt(n + 2) | 32;
      if (pathname[n + 1] === "2" && third === 102 ||
      // 2f 2F /
      pathname[n + 1] === "5" && third === 99) {
        throw new _errors.ERR_INVALID_FILE_URL_PATH(String.raw`must not include encoded \ or / characters`);
      }
    }
  }
  pathname = pathname.replace(FORWARD_SLASH, "\\");
  pathname = decodeURIComponent(pathname);
  if (hostname !== "") {
    return `\\\\${punnycode.toUnicode(hostname)}${pathname}`;
  }
  const letter = String.prototype.codePointAt.call(pathname, 1) | 32;
  const sep = String.prototype.charAt.call(pathname, 2);
  if (letter < _constants.CHAR_LOWERCASE_A || letter > _constants.CHAR_LOWERCASE_Z ||
  // a..z A..Z
  sep !== ":") {
    throw new _errors.ERR_INVALID_FILE_URL_PATH("must be absolute");
  }
  return String.prototype.slice.call(pathname, 1);
}
function getPathFromURLPosix(url) {
  if (url.hostname !== "") {
    throw new _errors.ERR_INVALID_FILE_URL_HOST("??");
  }
  const pathname = url.pathname;
  for (let n = 0; n < pathname.length; n++) {
    if (pathname[n] === "%") {
      const third = String.prototype.codePointAt.call(pathname, n + 2) | 32;
      if (pathname[n + 1] === "2" && third === 102) {
        throw new _errors.ERR_INVALID_FILE_URL_PATH("must not include encoded / characters");
      }
    }
  }
  return decodeURIComponent(pathname);
}
function isURL(self) {
  return Boolean(self?.href && self.protocol && self.auth === void 0 && self.path === void 0);
}