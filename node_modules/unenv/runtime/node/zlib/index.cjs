"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  constants: true,
  codes: true
};
Object.defineProperty(exports, "codes", {
  enumerable: true,
  get: function () {
    return _codes.codes;
  }
});
Object.defineProperty(exports, "constants", {
  enumerable: true,
  get: function () {
    return _constants.constants;
  }
});

var _constants = require("./internal/constants.cjs");
var _codes = require("./internal/codes.cjs");
var _brotli = _interopRequireWildcard(require("./internal/formats/brotli.cjs"));
Object.keys(_brotli).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _brotli[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _brotli[key];
    }
  });
});
var _deflate = _interopRequireWildcard(require("./internal/formats/deflate.cjs"));
Object.keys(_deflate).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _deflate[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _deflate[key];
    }
  });
});
var _gzip = _interopRequireWildcard(require("./internal/formats/gzip.cjs"));
Object.keys(_gzip).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _gzip[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _gzip[key];
    }
  });
});
var _zip = _interopRequireWildcard(require("./internal/formats/zip.cjs"));
Object.keys(_zip).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _zip[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _zip[key];
    }
  });
});
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
const Z_BINARY = 0;
const Z_TEXT = 1;
const Z_ASCII = 1;
const Z_UNKNOWN = 2;
const Z_DEFLATED = 8;
module.exports = {
  ..._constants.constants,
  ..._brotli,
  ..._deflate,
  ..._gzip,
  ..._zip,
  // @ts-expect-error @types/node is missing this one - this is a bug in typings
  codes: _codes.codes,
  constants: _constants.constants,
  Z_BINARY,
  Z_TEXT,
  Z_ASCII,
  Z_UNKNOWN,
  Z_DEFLATED
};