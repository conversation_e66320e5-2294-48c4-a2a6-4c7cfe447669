import type zlib from "node:zlib";
import { ZlibCompress, ZLibDecompress } from "./_shared";
export declare class Deflate extends ZlibCompress {
    readonly _format = "deflate";
    params(level: number, strategy: number, callback: () => void): void;
    reset(): void;
}
export declare const deflate: typeof zlib.deflate;
export declare const createDeflate: typeof zlib.createDeflate;
export declare const deflateSync: typeof zlib.deflateSync;
export declare class Inflate extends ZLibDecompress {
    readonly _format = "deflate";
    reset(): void;
}
export declare const inflate: typeof zlib.inflate;
export declare const createInflate: typeof zlib.createInflate;
export declare const inflateSync: typeof zlib.inflateSync;
export declare class DeflateRaw extends Deflate {
}
export declare const deflateRaw: typeof zlib.deflateRaw;
export declare const createDeflateRaw: typeof zlib.createDeflateRaw;
export declare const deflateRawSync: typeof zlib.deflateRawSync;
export declare class InflateRaw extends Inflate {
}
export declare const inflateRaw: typeof zlib.inflateRaw;
export declare const createInflateRaw: typeof zlib.createInflateRaw;
export declare const inflateRawSync: typeof zlib.inflateRawSync;
