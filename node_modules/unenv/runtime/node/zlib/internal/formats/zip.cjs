"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.unzipSync = exports.unzip = exports.createUnzip = exports.Unzip = void 0;
var _utils = require("../../../../_internal/utils.cjs");
var _shared = require("./_shared.cjs");
class Unzip extends _shared.ZlibCompress {
  _format = "zip";
}
exports.Unzip = Unzip;
const createUnzip = () => new Unzip();
exports.createUnzip = createUnzip;
const unzip = exports.unzip = (0, _utils.notImplemented)("zlib.unzip");
const unzipSync = exports.unzipSync = (0, _utils.notImplemented)("zlib.unzipSync");