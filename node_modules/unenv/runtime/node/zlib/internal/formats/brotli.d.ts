import type zlib from "node:zlib";
import { ZlibCompress, ZLibDecompress } from "./_shared";
export declare class BrotliCompress extends ZlibCompress {
    readonly _format = "brotli";
}
export declare const brotliCompress: typeof zlib.brotliCompress;
export declare const createBrotliCompress: typeof zlib.createBrotliCompress;
export declare const brotliCompressSync: typeof zlib.brotliCompressSync;
export declare class BrotliDecompress extends ZLibDecompress {
    readonly _format = "brotli";
}
export declare const brotliDecompress: typeof zlib.brotliDecompress;
export declare const createBrotliDecompress: typeof zlib.createBrotliDecompress;
export declare const brotliDecompressSync: typeof zlib.brotliDecompressSync;
