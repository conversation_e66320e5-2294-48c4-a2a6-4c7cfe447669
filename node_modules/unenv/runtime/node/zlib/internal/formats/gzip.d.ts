import type zlib from "node:zlib";
import { ZlibCompress, ZLibDecompress } from "./_shared";
export declare class Gzip extends ZlibCompress {
    readonly _format = "gzip";
}
export declare const gzip: typeof zlib.gzip;
export declare const createGzip: typeof zlib.createGzip;
export declare const gzipSync: typeof zlib.gzipSync;
export declare class Gunzip extends ZLibDecompress {
    readonly _format = "gzip";
}
export declare const gunzip: typeof zlib.gunzip;
export declare const createGunzip: typeof zlib.createGunzip;
export declare const gunzipSync: typeof zlib.gunzipSync;
