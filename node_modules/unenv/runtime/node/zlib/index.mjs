import { constants } from "./internal/constants.mjs";
import { codes } from "./internal/codes.mjs";
import * as _brotli from "./internal/formats/brotli.mjs";
import * as _deflate from "./internal/formats/deflate.mjs";
import * as _gzip from "./internal/formats/gzip.mjs";
import * as _zip from "./internal/formats/zip.mjs";
export { constants } from "./internal/constants.mjs";
export { codes } from "./internal/codes.mjs";
export * from "./internal/formats/brotli.mjs";
export * from "./internal/formats/deflate.mjs";
export * from "./internal/formats/gzip.mjs";
export * from "./internal/formats/zip.mjs";
const Z_BINARY = 0;
const Z_TEXT = 1;
const Z_ASCII = 1;
const Z_UNKNOWN = 2;
const Z_DEFLATED = 8;
export default {
  ...constants,
  ..._brotli,
  ..._deflate,
  ..._gzip,
  ..._zip,
  // @ts-expect-error @types/node is missing this one - this is a bug in typings
  codes,
  constants,
  Z_BINARY,
  Z_TEXT,
  Z_ASCII,
  Z_UNKNOWN,
  Z_DEFLATED
};
