"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "ReadStream", {
  enumerable: true,
  get: function () {
    return _readStream.ReadStream;
  }
});
Object.defineProperty(exports, "WriteStream", {
  enumerable: true,
  get: function () {
    return _writeStream.WriteStream;
  }
});
exports.isatty = exports.default = void 0;
var _readStream = require("./internal/read-stream.cjs");
var _writeStream = require("./internal/write-stream.cjs");
const isatty = function () {
  return false;
};
exports.isatty = isatty;
module.exports = {
  ReadStream: _readStream.ReadStream,
  WriteStream: _writeStream.WriteStream,
  isatty
};