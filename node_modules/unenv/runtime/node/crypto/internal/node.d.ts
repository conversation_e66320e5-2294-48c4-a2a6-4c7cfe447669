import type nodeCrypto from "node:crypto";
export declare const webcrypto: any;
export declare const randomBytes: typeof nodeCrypto.randomBytes;
export declare const fips: typeof nodeCrypto.fips;
export declare const constants: typeof nodeCrypto.constants;
export declare const checkPrime: any;
export declare const checkPrimeSync: any;
/** @deprecated */
export declare const createCipher: (...args: any) => any;
/** @deprecated */
export declare const createDecipher: (...args: any) => any;
export declare const pseudoRandomBytes: any;
export declare const createCipheriv: any;
export declare const createDecipheriv: any;
export declare const createDiffieHellman: any;
export declare const createDiffieHellmanGroup: any;
export declare const createECDH: any;
export declare const createHash: any;
export declare const createHmac: any;
export declare const createPrivateKey: any;
export declare const createPublicKey: any;
export declare const createSecretKey: any;
export declare const createSign: any;
export declare const createVerify: any;
export declare const diffieHellman: any;
export declare const generatePrime: any;
export declare const generatePrimeSync: any;
export declare const getCiphers: any;
export declare const getCipherInfo: any;
export declare const getCurves: any;
export declare const getDiffieHellman: any;
export declare const getHashes: any;
export declare const hkdf: any;
export declare const hkdfSync: any;
export declare const pbkdf2: any;
export declare const pbkdf2Sync: any;
export declare const generateKeyPair: any;
export declare const generateKeyPairSync: any;
export declare const generateKey: any;
export declare const generateKeySync: any;
export declare const privateDecrypt: any;
export declare const privateEncrypt: any;
export declare const publicDecrypt: any;
export declare const publicEncrypt: any;
export declare const randomFill: any;
export declare const randomFillSync: any;
export declare const randomInt: any;
export declare const scrypt: any;
export declare const scryptSync: any;
export declare const sign: any;
export declare const setEngine: any;
export declare const timingSafeEqual: any;
export declare const getFips: any;
export declare const setFips: any;
export declare const verify: any;
export declare const secureHeapUsed: any;
export declare const hash: any;
export declare const Certificate: typeof nodeCrypto.Certificate;
export declare const Cipher: typeof nodeCrypto.Cipher;
export declare const Cipheriv: typeof nodeCrypto.Cipheriv;
export declare const Decipher: typeof nodeCrypto.Decipher;
export declare const Decipheriv: typeof nodeCrypto.Decipheriv;
export declare const DiffieHellman: typeof nodeCrypto.DiffieHellman;
export declare const DiffieHellmanGroup: typeof nodeCrypto.DiffieHellmanGroup;
export declare const ECDH: typeof nodeCrypto.ECDH;
export declare const Hash: typeof nodeCrypto.Hash;
export declare const Hmac: typeof nodeCrypto.Hmac;
export declare const KeyObject: typeof nodeCrypto.KeyObject;
export declare const Sign: typeof nodeCrypto.Sign;
export declare const Verify: typeof nodeCrypto.Verify;
export declare const X509Certificate: typeof nodeCrypto.X509Certificate;
