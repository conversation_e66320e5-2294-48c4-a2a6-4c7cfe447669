declare const constants: {
    ALPN_ENABLED: number;
    DH_CHECK_P_NOT_PRIME: number;
    DH_CHECK_P_NOT_SAFE_PRIME: number;
    DH_NOT_SUITABLE_GENERATOR: number;
    DH_UNABLE_TO_CHECK_GENERATOR: number;
    ENGINE_METHOD_ALL: number;
    ENGINE_METHOD_CIPHERS: number;
    ENGINE_METHOD_DH: number;
    ENGINE_METHOD_DIGESTS: number;
    ENGINE_METHOD_DSA: number;
    ENGINE_METHOD_EC: number;
    ENGINE_METHOD_NONE: number;
    ENGINE_METHOD_PKEY_ASN1_METHS: number;
    ENGINE_METHOD_PKEY_METHS: number;
    ENGINE_METHOD_RAND: number;
    ENGINE_METHOD_RSA: number;
    OPENSSL_VERSION_NUMBER: number;
    POINT_CONVERSION_COMPRESSED: number;
    POINT_CONVERSION_HYBRID: number;
    POINT_CONVERSION_UNCOMPRESSED: number;
    RSA_NO_PADDING: number;
    RSA_PKCS1_OAEP_PADDING: number;
    RSA_PKCS1_PADDING: number;
    RSA_PKCS1_PSS_PADDING: number;
    RSA_PSS_SALTLEN_AUTO: number;
    RSA_PSS_SALTLEN_DIGEST: number;
    RSA_PSS_SALTLEN_MAX_SIGN: number;
    RSA_X931_PADDING: number;
    SSL_OP_ALL: number;
    SSL_OP_ALLOW_NO_DHE_KEX: number;
    SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION: number;
    SSL_OP_CIPHER_SERVER_PREFERENCE: number;
    SSL_OP_CISCO_ANYCONNECT: number;
    SSL_OP_COOKIE_EXCHANGE: number;
    SSL_OP_CRYPTOPRO_TLSEXT_BUG: number;
    SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS: number;
    SSL_OP_EPHEMERAL_RSA: number;
    SSL_OP_LEGACY_SERVER_CONNECT: number;
    SSL_OP_MICROSOFT_BIG_SSLV3_BUFFER: number;
    SSL_OP_MICROSOFT_SESS_ID_BUG: number;
    SSL_OP_MSIE_SSLV2_RSA_PADDING: number;
    SSL_OP_NETSCAPE_CA_DN_BUG: number;
    SSL_OP_NETSCAPE_CHALLENGE_BUG: number;
    SSL_OP_NETSCAPE_DEMO_CIPHER_CHANGE_BUG: number;
    SSL_OP_NETSCAPE_REUSE_CIPHER_CHANGE_BUG: number;
    SSL_OP_NO_COMPRESSION: number;
    SSL_OP_NO_ENCRYPT_THEN_MAC: number;
    SSL_OP_NO_QUERY_MTU: number;
    SSL_OP_NO_RENEGOTIATION: number;
    SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION: number;
    SSL_OP_NO_SSLv2: number;
    SSL_OP_NO_SSLv3: number;
    SSL_OP_NO_TICKET: number;
    SSL_OP_NO_TLSv1_1: number;
    SSL_OP_NO_TLSv1_2: number;
    SSL_OP_NO_TLSv1_3: number;
    SSL_OP_NO_TLSv1: number;
    SSL_OP_PKCS1_CHECK_1: number;
    SSL_OP_PKCS1_CHECK_2: number;
    SSL_OP_PRIORITIZE_CHACHA: number;
    SSL_OP_SINGLE_DH_USE: number;
    SSL_OP_SINGLE_ECDH_USE: number;
    SSL_OP_SSLEAY_080_CLIENT_DH_BUG: number;
    SSL_OP_SSLREF2_REUSE_CERT_TYPE_BUG: number;
    SSL_OP_TLS_BLOCK_PADDING_BUG: number;
    SSL_OP_TLS_D5_BUG: number;
    SSL_OP_TLS_ROLLBACK_BUG: number;
    TLS1_1_VERSION: number;
    TLS1_2_VERSION: number;
    TLS1_3_VERSION: number;
    TLS1_VERSION: number;
    defaultCoreCipherList: string;
    defaultCipherList: string;
};
export default constants;
