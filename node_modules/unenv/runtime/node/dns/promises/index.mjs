import noop from "../../../mock/noop.mjs";
import mock from "../../../mock/proxy.mjs";
import { notImplemented, notImplementedAsync } from "../../../_internal/utils.mjs";
import * as constants from "../internal/constants.mjs";
export * from "../internal/constants.mjs";
export const Resolver = mock.__createMock__("dns.Resolver");
export const getDefaultResultOrder = () => "verbatim";
export const getServers = () => [];
export const lookup = notImplementedAsync("dns.lookup");
export const lookupService = notImplementedAsync("dns.lookupService");
export const resolve = notImplementedAsync("dns.resolve");
export const resolve4 = notImplementedAsync("dns.resolve4");
export const resolve6 = notImplementedAsync("dns.resolve6");
export const resolveAny = notImplementedAsync("dns.resolveAny");
export const resolveCaa = notImplementedAsync("dns.resolveCaa");
export const resolveCname = notImplementedAsync("dns.resolveCname");
export const resolveMx = notImplementedAsync("dns.resolveMx");
export const resolveNaptr = notImplementedAsync("dns.resolveNaptr");
export const resolveNs = notImplementedAsync("dns.resolveNs");
export const resolvePtr = notImplementedAsync("dns.resolvePtr");
export const resolveSoa = notImplementedAsync("dns.resolveSoa");
export const resolveSrv = notImplementedAsync("dns.resolveSrv");
export const resolveTxt = notImplementedAsync("dns.resolveTxt");
export const reverse = notImplemented("dns.reverse");
export const setDefaultResultOrder = noop;
export const setServers = noop;
export default {
  ...constants,
  Resolver,
  getDefaultResultOrder,
  getServers,
  lookup,
  lookupService,
  resolve,
  resolve4,
  resolve6,
  resolveAny,
  resolveCaa,
  resolveCname,
  resolveMx,
  resolveNaptr,
  resolveNs,
  resolvePtr,
  resolveSoa,
  resolveSrv,
  resolveTxt,
  reverse,
  setDefaultResultOrder,
  setServers
};
