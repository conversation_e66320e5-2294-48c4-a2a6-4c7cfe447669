import type dns from "node:dns/promises";
export * from "../internal/constants";
export declare const Resolver: typeof dns.Resolver;
export declare const getDefaultResultOrder: typeof dns.getDefaultResultOrder;
export declare const getServers: typeof dns.getServers;
export declare const lookup: typeof dns.lookup;
export declare const lookupService: typeof dns.lookupService;
export declare const resolve: typeof dns.resolve;
export declare const resolve4: typeof dns.resolve4;
export declare const resolve6: typeof dns.resolve6;
export declare const resolveAny: typeof dns.resolveAny;
export declare const resolveCaa: typeof dns.resolveCaa;
export declare const resolveCname: typeof dns.resolveCname;
export declare const resolveMx: typeof dns.resolveMx;
export declare const resolveNaptr: typeof dns.resolveNaptr;
export declare const resolveNs: typeof dns.resolveNs;
export declare const resolvePtr: typeof dns.resolvePtr;
export declare const resolveSoa: typeof dns.resolveSoa;
export declare const resolveSrv: typeof dns.resolveSrv;
export declare const resolveTxt: typeof dns.resolveTxt;
export declare const reverse: typeof dns.reverse;
export declare const setDefaultResultOrder: typeof dns.setDefaultResultOrder;
export declare const setServers: typeof dns.setServers;
declare const _default: typeof dns;
export default _default;
