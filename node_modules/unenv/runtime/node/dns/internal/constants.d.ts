import type dns from "node:dns";
export declare const ADDRCONFIG: typeof dns.ADDRCONF<PERSON>;
export declare const ALL: typeof dns.ALL;
export declare const V4MAPPED: typeof dns.V4MAPPED;
export declare const ADDRGETNETWORKPARAMS: typeof dns.ADDRGETNETWORKPARAMS;
export declare const BADFAMILY: typeof dns.BADFAMILY;
export declare const BADFLAGS: typeof dns.BADFLAGS;
export declare const BADHINTS: typeof dns.BADHINTS;
export declare const BADNAME: typeof dns.BADNAME;
export declare const BADQUERY: typeof dns.BADQUERY;
export declare const BADRESP: typeof dns.BADRESP;
export declare const BADSTR: typeof dns.BADSTR;
export declare const CANCELLED: typeof dns.CANCELLED;
export declare const CONNREFUSED: typeof dns.CONNREFUSED;
export declare const DESTRUCTION: typeof dns.DESTRUCTION;
export declare const EOF: typeof dns.EOF;
export declare const FILE: typeof dns.FILE;
export declare const FORMERR: typeof dns.FORMERR;
export declare const LOADIPHLPAPI: typeof dns.LOADIPHLPAPI;
export declare const NODATA: typeof dns.NODATA;
export declare const NOMEM: typeof dns.NOMEM;
export declare const NONAME: typeof dns.NONAME;
export declare const NOTFOUND: typeof dns.NOTFOUND;
export declare const NOTIMP: typeof dns.NOTIMP;
export declare const NOTINITIALIZED: typeof dns.NOTINITIALIZED;
export declare const REFUSED: typeof dns.REFUSED;
export declare const SERVFAIL: typeof dns.SERVFAIL;
export declare const TIMEOUT: typeof dns.TIMEOUT;
