"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.encodeStr = encodeStr;
exports.isHexTable = exports.hexTable = void 0;
class ERR_INVALID_URI extends URIError {
  code = "ERR_INVALID_URI";
  constructor() {
    super("URI malformed");
  }
}
const hexTable = exports.hexTable = Array.from({
  length: 256
});
for (let i = 0; i < 256; ++i) hexTable[i] = "%" + String.prototype.toUpperCase.call((i < 16 ? "0" : "") + Number.prototype.toString.call(i, 16));
const isHexTable = exports.isHexTable = new Int8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
// 0 - 15
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
// 16 - 31
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
// 32 - 47
1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,
// 48 - 63
0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
// 64 - 79
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
// 80 - 95
0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
// 96 - 111
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
// 112 - 127
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
// 128 ...
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
// ... 256
]);
function encodeStr(str, noEscapeTable, hexTable2) {
  const len = str.length;
  if (len === 0) return "";
  let out = "";
  let lastPos = 0;
  let i = 0;
  outer: for (; i < len; i++) {
    let c = String.prototype.charCodeAt.call(str, i);
    while (c < 128) {
      if (noEscapeTable[c] !== 1) {
        if (lastPos < i) out += String.prototype.slice.call(str, lastPos, i);
        lastPos = i + 1;
        out += hexTable2[c];
      }
      if (++i === len) break outer;
      c = String.prototype.charCodeAt.call(str, i);
    }
    if (lastPos < i) out += String.prototype.slice.call(str, lastPos, i);
    if (c < 2048) {
      lastPos = i + 1;
      out += hexTable2[192 | c >> 6] + hexTable2[128 | c & 63];
      continue;
    }
    if (c < 55296 || c >= 57344) {
      lastPos = i + 1;
      out += hexTable2[224 | c >> 12] + hexTable2[128 | c >> 6 & 63] + hexTable2[128 | c & 63];
      continue;
    }
    ++i;
    if (i >= len) throw new ERR_INVALID_URI();
    const c2 = String.prototype.charCodeAt.call(str, i) & 1023;
    lastPos = i + 1;
    c = 65536 + ((c & 1023) << 10 | c2);
    out += hexTable2[240 | c >> 18] + hexTable2[128 | c >> 12 & 63] + hexTable2[128 | c >> 6 & 63] + hexTable2[128 | c & 63];
  }
  if (lastPos === 0) return str;
  if (lastPos < len) return out + String.prototype.slice.call(str, lastPos);
  return out;
}