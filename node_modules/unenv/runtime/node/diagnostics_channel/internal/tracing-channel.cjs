"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TracingChannel = void 0;
var _utils = require("../../../_internal/utils.cjs");
var _channel = require("./channel.cjs");
class TracingChannel {
  __unenv__ = true;
  asyncEnd = new _channel.Channel("asyncEnd");
  asyncStart = new _channel.Channel("asyncStart");
  end = new _channel.Channel("end");
  error = new _channel.Channel("error");
  start = new _channel.Channel("start");
  constructor(nameOrChannels) {
    if (typeof nameOrChannels === "string") {
      this.asyncEnd = new _channel.Channel(`trace:${nameOrChannels}:asyncEnd`);
      this.asyncStart = new _channel.Channel(`trace:${nameOrChannels}:asyncStart`);
      this.end = new _channel.Channel(`trace:${nameOrChannels}:end`);
      this.error = new _channel.Channel(`trace:${nameOrChannels}:error`);
      this.start = new _channel.Channel(`trace:${nameOrChannels}:start`);
    } else {
      this.asyncStart = nameOrChannels.asyncStart;
      this.asyncEnd = nameOrChannels.asyncEnd;
      this.end = nameOrChannels.end;
      this.error = nameOrChannels.error;
      this.start = nameOrChannels.start;
    }
  }
  subscribe(handlers) {
    this.asyncEnd?.subscribe(handlers.asyncEnd);
    this.asyncStart?.subscribe(handlers.asyncStart);
    this.end?.subscribe(handlers.end);
    this.error?.subscribe(handlers.error);
    this.start?.subscribe(handlers.start);
  }
  unsubscribe(handlers) {
    this.asyncEnd?.unsubscribe(handlers.asyncEnd);
    this.asyncStart?.unsubscribe(handlers.asyncStart);
    this.end?.unsubscribe(handlers.end);
    this.error?.unsubscribe(handlers.error);
    this.start?.unsubscribe(handlers.start);
  }
  traceSync() {
    throw (0, _utils.createNotImplementedError)("TracingChannel.traceSync");
  }
  tracePromise() {
    throw (0, _utils.createNotImplementedError)("TracingChannel.tracePromise");
  }
  traceCallback() {
    throw (0, _utils.createNotImplementedError)("TracingChannel.traceCallback");
  }
}
exports.TracingChannel = TracingChannel;