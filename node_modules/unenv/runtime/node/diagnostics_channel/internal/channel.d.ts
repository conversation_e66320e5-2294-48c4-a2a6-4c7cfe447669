import type diagnostics_channel from "node:diagnostics_channel";
export declare const getChannels: () => Record<string | symbol, diagnostics_channel.Channel>;
export declare class Channel<StoreType, ContextType> implements diagnostics_channel.Channel<StoreType, ContextType> {
    readonly __unenv__ = true;
    name: diagnostics_channel.Channel["name"];
    get hasSubscribers(): boolean;
    _subscribers: diagnostics_channel.ChannelListener[];
    constructor(name: diagnostics_channel.Channel["name"]);
    subscribe(onMessage: diagnostics_channel.ChannelListener): void;
    unsubscribe(onMessage: diagnostics_channel.ChannelListener): boolean;
    publish(message: unknown): void;
    bindStore(): void;
    unbindStore(): void;
    runStores(): void;
}
