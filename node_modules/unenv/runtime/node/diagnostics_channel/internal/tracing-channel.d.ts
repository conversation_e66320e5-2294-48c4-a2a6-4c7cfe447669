import type diagnostics_channel from "node:diagnostics_channel";
import { Channel } from "./channel";
export declare class TracingChannel<StoreType = unknown, ContextType extends object = object> implements diagnostics_channel.TracingChannel<StoreType, ContextType> {
    readonly __unenv__ = true;
    asyncEnd: Channel<StoreType, ContextType>;
    asyncStart: Channel<StoreType, ContextType>;
    end: Channel<StoreType, ContextType>;
    error: Channel<StoreType, ContextType>;
    start: Channel<StoreType, ContextType>;
    constructor(nameOrChannels: string | diagnostics_channel.TracingChannelCollection<StoreType, ContextType>);
    subscribe(handlers: diagnostics_channel.TracingChannelSubscribers<ContextType>): void;
    unsubscribe(handlers: diagnostics_channel.TracingChannelSubscribers<ContextType>): void;
    traceSync(): void;
    tracePromise(): void;
    traceCallback(): void;
}
