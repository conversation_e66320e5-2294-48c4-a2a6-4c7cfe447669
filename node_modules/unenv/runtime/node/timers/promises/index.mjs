import { Scheduler } from "../internal/scheduler.mjs";
import { setTimeoutFallbackPromises } from "../internal/set-timeout.mjs";
import { setIntervalFallbackPromises } from "../internal/set-interval.mjs";
import { setImmediateFallbackPromises } from "../internal/set-immediate.mjs";
export { setTimeoutFallbackPromises as setTimeout } from "../internal/set-timeout.mjs";
export { setIntervalFallbackPromises as setInterval } from "../internal/set-interval.mjs";
export { setImmediateFallbackPromises as setImmediate } from "../internal/set-immediate.mjs";
export const scheduler = new Scheduler();
export default {
  scheduler,
  setImmediate: setImmediateFallbackPromises,
  setInterval: setIntervalFallbackPromises,
  setTimeout: setTimeoutFallbackPromises
};
