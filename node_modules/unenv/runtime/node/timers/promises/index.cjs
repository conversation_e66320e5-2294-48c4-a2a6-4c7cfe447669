"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.scheduler = exports.default = void 0;
Object.defineProperty(exports, "setImmediate", {
  enumerable: true,
  get: function () {
    return _setImmediate.setImmediateFallbackPromises;
  }
});
Object.defineProperty(exports, "setInterval", {
  enumerable: true,
  get: function () {
    return _setInterval.setIntervalFallbackPromises;
  }
});
Object.defineProperty(exports, "setTimeout", {
  enumerable: true,
  get: function () {
    return _setTimeout.setTimeoutFallbackPromises;
  }
});
var _scheduler = require("../internal/scheduler.cjs");
var _setTimeout = require("../internal/set-timeout.cjs");
var _setInterval = require("../internal/set-interval.cjs");
var _setImmediate = require("../internal/set-immediate.cjs");
const scheduler = exports.scheduler = new _scheduler.Scheduler();
module.exports = {
  scheduler,
  setImmediate: _setImmediate.setImmediateFallbackPromises,
  setInterval: _setInterval.setIntervalFallbackPromises,
  setTimeout: _setTimeout.setTimeoutFallbackPromises
};