import type timers from "node:timers/promises";
import { Scheduler } from "../internal/scheduler";
export { setTimeoutFallbackPromises as setTimeout } from "../internal/set-timeout";
export { setIntervalFallbackPromises as setInterval } from "../internal/set-interval";
export { setImmediateFallbackPromises as setImmediate } from "../internal/set-immediate";
export declare const scheduler: Scheduler;
declare const _default: typeof timers;
export default _default;
