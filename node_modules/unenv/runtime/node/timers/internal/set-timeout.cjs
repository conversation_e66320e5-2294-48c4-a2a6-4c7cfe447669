"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.setTimeoutFallback = setTimeoutFallback;
exports.setTimeoutFallbackPromises = setTimeoutFallbackPromises;
var _timeout = require("./timeout.cjs");
function setTimeoutFallbackPromises(delay, value) {
  return new Promise(res => {
    res(value);
  });
}
function setTimeoutFallback(callback, ms, ...args) {
  return new _timeout.Timeout(callback, args);
}
setTimeoutFallback.__promisify__ = setTimeoutFallbackPromises;