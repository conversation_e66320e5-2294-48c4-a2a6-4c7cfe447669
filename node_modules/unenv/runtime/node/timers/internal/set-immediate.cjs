"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.clearImmediateFallback = clearImmediateFallback;
exports.setImmediateFallback = setImmediateFallback;
exports.setImmediateFallbackPromises = setImmediateFallbackPromises;
var _immediate = require("./immediate.cjs");
function setImmediateFallbackPromises(value) {
  return new Promise(res => {
    res(value);
  });
}
function setImmediateFallback(callback, ...args) {
  return new _immediate.Immediate(callback, args);
}
setImmediateFallback.__promisify__ = setImmediateFallbackPromises;
function clearImmediateFallback(immediate) {
  immediate?.[Symbol.dispose]();
}