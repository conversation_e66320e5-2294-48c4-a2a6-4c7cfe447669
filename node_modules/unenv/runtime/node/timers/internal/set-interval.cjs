"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.setIntervalFallback = setIntervalFallback;
exports.setIntervalFallbackPromises = setIntervalFallbackPromises;
var _timeout = require("./timeout.cjs");
async function* setIntervalFallbackPromises(delay, value) {
  yield value;
}
function setIntervalFallback(callback, ms, ...args) {
  return new _timeout.Timeout(callback, args);
}
setIntervalFallback.__promisify__ = setIntervalFallbackPromises;