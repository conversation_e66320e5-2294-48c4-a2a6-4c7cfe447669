"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Script", {
  enumerable: true,
  get: function () {
    return _script.Script;
  }
});
exports.runInThisContext = exports.runInNewContext = exports.runInContext = exports.measureMemory = exports.isContext = exports.default = exports.createScript = exports.createContext = exports.constants = exports.compileFunction = void 0;
var _utils = require("../../_internal/utils.cjs");
var _script = require("./internal/script.cjs");
var constants = _interopRequireWildcard(require("./internal/constants.cjs"));
var _constants = constants;
exports.constants = constants;
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
const compileFunction = exports.compileFunction = (0, _utils.notImplemented)("vm.compileFunction");
const _contextSymbol = Symbol("uenv.vm.context");
const createContext = exports.createContext = function createContext2() {
  return Object.create(null, {
    [_contextSymbol]: {
      value: true
    }
  });
};
const createScript = exports.createScript = function createScript2() {
  return new _script.Script();
};
const isContext = context => {
  return context && context[_contextSymbol] === true;
};
exports.isContext = isContext;
const measureMemory = () => Promise.resolve({
  total: {
    jsMemoryEstimate: 0,
    jsMemoryRange: [1, 2]
  },
  WebAssembly: {
    code: 0,
    metadata: 0
  }
});
exports.measureMemory = measureMemory;
const runInContext = exports.runInContext = (0, _utils.notImplemented)("vm.runInContext");
const runInNewContext = exports.runInNewContext = (0, _utils.notImplemented)("vm.runInNewContext");
const runInThisContext = exports.runInThisContext = (0, _utils.notImplemented)("vm.runInThisContext");
module.exports = {
  Script: _script.Script,
  compileFunction,
  constants,
  createContext,
  createScript,
  isContext,
  measureMemory,
  runInContext,
  runInNewContext,
  runInThisContext
};