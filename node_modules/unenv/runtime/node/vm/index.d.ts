import type vm from "node:vm";
import { <PERSON>ript } from "./internal/script";
export { <PERSON>ript } from "./internal/script";
export * as constants from "./internal/constants";
export declare const compileFunction: typeof vm.compileFunction;
export declare const createContext: typeof vm.createContext;
export declare const createScript: () => Script;
export declare const isContext: typeof vm.isContext;
export declare const measureMemory: typeof vm.measureMemory;
export declare const runInContext: typeof vm.runInContext;
export declare const runInNewContext: typeof vm.runInNewContext;
export declare const runInThisContext: typeof vm.runInThisContext;
declare const _default: Omit<typeof vm, "Module" | "SourceTextModule" | "SyntheticModule">;
export default _default;
