"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Script = void 0;
var _utils = require("../../../_internal/utils.cjs");
class Script {
  runInContext(contextifiedObject, options) {
    throw (0, _utils.createNotImplementedError)("Script.runInContext");
  }
  runInNewContext(contextObject, options) {
    throw (0, _utils.createNotImplementedError)("Script.runInNewContext");
  }
  runInThisContext(options) {
    throw (0, _utils.createNotImplementedError)("Script.runInThisContext");
  }
  createCachedData() {
    throw (0, _utils.createNotImplementedError)("Script.createCachedData");
  }
}
exports.Script = Script;