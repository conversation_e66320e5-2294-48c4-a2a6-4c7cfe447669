import type vm from "node:vm";
export declare class Script implements vm.Script {
    runInContext(contextifiedObject: vm.Context, options?: vm.RunningScriptOptions | undefined): void;
    runInNewContext(contextObject?: vm.Context | undefined, options?: vm.RunningScriptInNewContextOptions | undefined): void;
    runInThisContext(options?: vm.RunningScriptOptions | undefined): void;
    createCachedData(): Buffer;
}
