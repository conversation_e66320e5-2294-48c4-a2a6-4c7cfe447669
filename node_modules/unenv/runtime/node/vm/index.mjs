import { notImplemented } from "../../_internal/utils.mjs";
import { Script } from "./internal/script.mjs";
import * as constants from "./internal/constants.mjs";
export { Script } from "./internal/script.mjs";
export * as constants from "./internal/constants.mjs";
export const compileFunction = notImplemented("vm.compileFunction");
const _contextSymbol = Symbol("uenv.vm.context");
export const createContext = function createContext2() {
  return Object.create(null, {
    [_contextSymbol]: {
      value: true
    }
  });
};
export const createScript = function createScript2() {
  return new Script();
};
export const isContext = (context) => {
  return context && context[_contextSymbol] === true;
};
export const measureMemory = () => Promise.resolve({
  total: { jsMemoryEstimate: 0, jsMemoryRange: [1, 2] },
  WebAssembly: { code: 0, metadata: 0 }
});
export const runInContext = notImplemented("vm.runInContext");
export const runInNewContext = notImplemented("vm.runInNewContext");
export const runInThisContext = notImplemented(
  "vm.runInThisContext"
);
export default {
  Script,
  compileFunction,
  constants,
  createContext,
  createScript,
  isContext,
  measureMemory,
  runInContext,
  runInNewContext,
  runInThisContext
};
