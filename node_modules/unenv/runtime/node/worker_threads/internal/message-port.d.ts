import { EventEmitter } from "node:events";
import type worker_threads from "node:worker_threads";
export declare class MessagePort extends EventEmitter implements worker_threads.MessagePort {
    close(): void;
    postMessage(value: any, transferList?: readonly worker_threads.TransferListItem[] | undefined): void;
    ref(): void;
    unref(): void;
    start(): void;
    addEventListener(type: string, listener: (...args: any[]) => void): void;
    removeEventListener(type: string, listener: (...args: any[]) => void): void;
    dispatchEvent(event: Event): boolean;
}
