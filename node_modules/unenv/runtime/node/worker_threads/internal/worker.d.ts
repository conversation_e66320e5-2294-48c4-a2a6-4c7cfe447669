import { EventEmitter } from "node:events";
import type worker_threads from "node:worker_threads";
import { Readable } from "node:stream";
export declare class Worker extends EventEmitter implements worker_threads.Worker {
    stdin: null;
    stdout: Readable;
    stderr: Readable;
    threadId: number;
    performance: {
        eventLoopUtilization: () => {
            idle: number;
            active: number;
            utilization: number;
        };
    };
    postMessage(_value: any, _transferList?: readonly worker_threads.TransferListItem[] | undefined): void;
    postMessageToThread(_threadId: unknown, _value: unknown, _transferList?: unknown, _timeout?: unknown): Promise<void>;
    ref(): void;
    unref(): void;
    terminate(): Promise<number>;
    getHeapSnapshot(): Promise<Readable>;
}
