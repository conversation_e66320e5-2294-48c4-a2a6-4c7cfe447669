"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Worker = void 0;
var _nodeEvents = require("node:events");
var _nodeStream = require("node:stream");
class Worker extends _nodeEvents.EventEmitter {
  stdin = null;
  stdout = new _nodeStream.Readable();
  stderr = new _nodeStream.Readable();
  threadId = 0;
  performance = {
    eventLoopUtilization: () => ({
      idle: 0,
      active: 0,
      utilization: 0
    })
  };
  postMessage(_value, _transferList) {}
  postMessageToThread(_threadId, _value, _transferList, _timeout) {
    return Promise.resolve();
  }
  ref() {}
  unref() {}
  terminate() {
    return Promise.resolve(0);
  }
  getHeapSnapshot() {
    return Promise.resolve(new _nodeStream.Readable());
  }
}
exports.Worker = Worker;