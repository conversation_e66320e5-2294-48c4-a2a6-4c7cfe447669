"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MessagePort = void 0;
var _nodeEvents = require("node:events");
class MessagePort extends _nodeEvents.EventEmitter {
  close() {}
  postMessage(value, transferList) {}
  ref() {}
  unref() {}
  start() {}
  addEventListener(type, listener) {
    this.on(type, listener);
  }
  removeEventListener(type, listener) {
    this.off(type, listener);
  }
  dispatchEvent(event) {
    return this.emit(event.type, event);
  }
}
exports.MessagePort = MessagePort;