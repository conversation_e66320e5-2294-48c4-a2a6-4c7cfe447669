import { notImplemented } from "../../_internal/utils.mjs";
import inherits from "../../npm/inherits.mjs";
import * as legacyTypes from "./internal/legacy-types.mjs";
import * as logUtils from "./internal/log.mjs";
import types from "./types/index.mjs";
import { promisify } from "./internal/promisify.mjs";
import * as mime from "./internal/mime.mjs";
export * from "./internal/mime.mjs";
export * from "./internal/legacy-types.mjs";
export * from "./internal/log.mjs";
export { default as inherits } from "../../npm/inherits.mjs";
export { promisify } from "./internal/promisify.mjs";
export { default as types } from "./types/index.mjs";
export const TextDecoder = globalThis.TextDecoder;
export const TextEncoder = globalThis.TextEncoder;
export const deprecate = (fn) => fn;
export const _errnoException = notImplemented("util._errnoException");
export const _exceptionWithHostPort = notImplemented(
  "util._exceptionWithHostPort"
);
export const _extend = notImplemented("util._extend");
export const aborted = notImplemented("util.aborted");
export const callbackify = notImplemented("util.callbackify");
export const getSystemErrorMap = notImplemented(
  "util.getSystemErrorMap"
);
export const getSystemErrorName = notImplemented("util.getSystemErrorName");
export const toUSVString = notImplemented("util.toUSVString");
export const stripVTControlCharacters = notImplemented("util.stripVTControlCharacters");
export const transferableAbortController = notImplemented("util.transferableAbortController");
export const transferableAbortSignal = notImplemented("util.transferableAbortSignal");
export const parseArgs = notImplemented("util.parseArgs");
export const parseEnv = notImplemented("util.parseEnv");
export const styleText = notImplemented("util.styleText");
export const getCallSite = notImplemented("util.getCallSites");
export default {
  _errnoException,
  _exceptionWithHostPort,
  _extend,
  aborted,
  callbackify,
  deprecate,
  getCallSite,
  getSystemErrorMap,
  getSystemErrorName,
  inherits,
  promisify,
  stripVTControlCharacters,
  toUSVString,
  TextDecoder,
  TextEncoder,
  types,
  transferableAbortController,
  transferableAbortSignal,
  parseArgs,
  parseEnv,
  styleText,
  ...mime,
  ...logUtils,
  ...legacyTypes
};
