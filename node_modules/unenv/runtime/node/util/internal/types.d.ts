import type utilTypes from "node:util/types";
export declare const isExternal: typeof utilTypes.isExternal;
export declare const isDate: typeof utilTypes.isDate;
export declare const isArgumentsObject: any;
export declare const isBigIntObject: (val: any) => val is Date;
export declare const isBooleanObject: typeof utilTypes.isBooleanObject;
export declare const isNumberObject: typeof utilTypes.isNumberObject;
export declare const isStringObject: typeof utilTypes.isStringObject;
export declare const isSymbolObject: typeof utilTypes.isSymbolObject;
export declare const isNativeError: any;
export declare const isRegExp: typeof utilTypes.isRegExp;
export declare const isAsyncFunction: any;
export declare const isGeneratorFunction: any;
export declare const isGeneratorObject: any;
export declare const isPromise: typeof utilTypes.isPromise;
export declare const isMap: typeof utilTypes.isMap;
export declare const isSet: typeof utilTypes.isSet;
export declare const isMapIterator: any;
export declare const isSetIterator: any;
export declare const isWeakMap: typeof utilTypes.isWeakMap;
export declare const isWeakSet: typeof utilTypes.isWeakSet;
export declare const isArrayBuffer: typeof utilTypes.isArrayBuffer;
export declare const isDataView: typeof utilTypes.isDataView;
export declare const isSharedArrayBuffer: typeof utilTypes.isSharedArrayBuffer;
export declare const isProxy: any;
export declare const isModuleNamespaceObject: any;
export declare const isAnyArrayBuffer: any;
export declare const isBoxedPrimitive: any;
export declare const isArrayBufferView: any;
export declare const isTypedArray: any;
export declare const isUint8Array: any;
export declare const isUint8ClampedArray: any;
export declare const isUint16Array: any;
export declare const isUint32Array: any;
export declare const isInt8Array: any;
export declare const isInt16Array: any;
export declare const isInt32Array: any;
export declare const isFloat32Array: any;
export declare const isFloat64Array: any;
export declare const isBigInt64Array: any;
export declare const isBigUint64Array: any;
export declare const isKeyObject: any;
export declare const isCryptoKey: any;
