import type util from "node:util";
export declare const isRegExp: typeof util.isRegExp;
export declare const isDate: typeof util.isDate;
export declare const isArray: typeof util.isArray;
export declare const isBoolean: typeof util.isBoolean;
export declare const isNull: typeof util.isNull;
export declare const isNullOrUndefined: typeof util.isNullOrUndefined;
export declare const isNumber: typeof util.isNumber;
export declare const isString: typeof util.isString;
export declare const isSymbol: typeof util.isSymbol;
export declare const isUndefined: typeof util.isUndefined;
export declare const isFunction: typeof util.isFunction;
export declare const isBuffer: typeof util.isBuffer;
export declare const isDeepStrictEqual: typeof util.isDeepStrictEqual;
export declare const isObject: typeof util.isObject;
export declare const isError: typeof util.isError;
export declare const isPrimitive: typeof util.isPrimitive;
