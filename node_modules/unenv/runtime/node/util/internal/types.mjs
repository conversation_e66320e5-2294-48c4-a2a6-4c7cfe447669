import { notImplemented } from "../../../_internal/utils.mjs";
export const isExternal = (_obj) => false;
export const isDate = (val) => val instanceof Date;
export const isArgumentsObject = notImplemented("util.types.isArgumentsObject");
export const isBigIntObject = (val) => val instanceof BigInt;
export const isBooleanObject = (val) => val instanceof Boolean;
export const isNumberObject = (val) => val instanceof Number;
export const isStringObject = (val) => val instanceof String;
export const isSymbolObject = (val) => val instanceof Symbol;
export const isNativeError = notImplemented(
  "util.types.isNativeError"
);
export const isRegExp = (val) => val instanceof RegExp;
export const isAsyncFunction = notImplemented(
  "util.types.isAsyncFunction"
);
export const isGeneratorFunction = notImplemented("util.types.isGeneratorFunction");
export const isGeneratorObject = notImplemented("util.types.isGeneratorObject");
export const isPromise = (val) => val instanceof Promise;
export const isMap = (val) => val instanceof Map;
export const isSet = (val) => val instanceof Set;
export const isMapIterator = notImplemented(
  "util.types.isMapIterator"
);
export const isSetIterator = notImplemented(
  "util.types.isSetIterator"
);
export const isWeakMap = (val) => val instanceof WeakMap;
export const isWeakSet = (val) => val instanceof WeakSet;
export const isArrayBuffer = (val) => val instanceof ArrayBuffer;
export const isDataView = (val) => val instanceof DataView;
export const isSharedArrayBuffer = (val) => val instanceof SharedArrayBuffer;
export const isProxy = notImplemented("util.types.isProxy");
export const isModuleNamespaceObject = notImplemented("util.types.isModuleNamespaceObject");
export const isAnyArrayBuffer = notImplemented("util.types.isAnyArrayBuffer");
export const isBoxedPrimitive = notImplemented("util.types.isBoxedPrimitive");
export const isArrayBufferView = notImplemented("util.types.isArrayBufferView");
export const isTypedArray = notImplemented(
  "util.types.isTypedArray"
);
export const isUint8Array = notImplemented(
  "util.types.isUint8Array"
);
export const isUint8ClampedArray = notImplemented("util.types.isUint8ClampedArray");
export const isUint16Array = notImplemented(
  "util.types.isUint16Array"
);
export const isUint32Array = notImplemented(
  "util.types.isUint32Array"
);
export const isInt8Array = notImplemented(
  "util.types.isInt8Array"
);
export const isInt16Array = notImplemented(
  "util.types.isInt16Array"
);
export const isInt32Array = notImplemented(
  "util.types.isInt32Array"
);
export const isFloat32Array = notImplemented(
  "util.types.isFloat32Array"
);
export const isFloat64Array = notImplemented(
  "util.types.isFloat64Array"
);
export const isBigInt64Array = notImplemented(
  "util.types.isBigInt64Array"
);
export const isBigUint64Array = notImplemented("util.types.isBigUint64Array");
export const isKeyObject = notImplemented(
  "util.types.isKeyObject"
);
export const isCryptoKey = notImplemented(
  "util.types.isCryptoKey"
);
