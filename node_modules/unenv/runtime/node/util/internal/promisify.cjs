"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.promisify = void 0;
const customSymbol = Symbol("customPromisify");
function _promisify(fn) {
  if (fn[customSymbol]) {
    return fn[customSymbol];
  }
  return function (...args) {
    return new Promise((resolve, reject) => {
      try {
        fn.call(this, ...args, (err, val) => {
          if (err) {
            return reject(err);
          }
          resolve(val);
        });
      } catch (error) {
        reject(error);
      }
    });
  };
}
_promisify.custom = customSymbol;
const promisify = exports.promisify = _promisify;