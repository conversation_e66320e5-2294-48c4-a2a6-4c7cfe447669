"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "DefaultDeserializer", {
  enumerable: true,
  get: function () {
    return _deserializer.DefaultDeserializer;
  }
});
Object.defineProperty(exports, "DefaultSerializer", {
  enumerable: true,
  get: function () {
    return _serializer.DefaultSerializer;
  }
});
Object.defineProperty(exports, "Deserializer", {
  enumerable: true,
  get: function () {
    return _deserializer.Deserializer;
  }
});
Object.defineProperty(exports, "GCProfiler", {
  enumerable: true,
  get: function () {
    return _profiler.GCProfiler;
  }
});
Object.defineProperty(exports, "Serializer", {
  enumerable: true,
  get: function () {
    return _serializer.Serializer;
  }
});
exports.promiseHooks = exports.getHeapStatistics = exports.getHeapSpaceStatistics = exports.getHeapSnapshot = exports.getHeapCodeStatistics = exports.deserialize = exports.default = exports.cachedDataVersionTag = void 0;
exports.queryObjects = queryObjects;
exports.writeHeapSnapshot = exports.takeCoverage = exports.stopCoverage = exports.startupSnapshot = exports.setHeapSnapshotNearHeapLimit = exports.setFlagsFromString = exports.serialize = void 0;
var _noop = _interopRequireDefault(require("../../mock/noop.cjs"));
var _nodeStream = require("node:stream");
var _deserializer = require("./internal/deserializer.cjs");
var _serializer = require("./internal/serializer.cjs");
var _profiler = require("./internal/profiler.cjs");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const getMockHeapSpaceStats = name => ({
  space_name: name,
  space_size: 0,
  space_used_size: 0,
  space_available_size: 0,
  physical_space_size: 0
});
const cachedDataVersionTag = () => 0;
exports.cachedDataVersionTag = cachedDataVersionTag;
const deserialize = exports.deserialize = _noop.default;
const getHeapCodeStatistics = () => ({
  code_and_metadata_size: 0,
  bytecode_and_metadata_size: 0,
  external_script_source_size: 0,
  cpu_profiler_metadata_size: 0
});
exports.getHeapCodeStatistics = getHeapCodeStatistics;
const getHeapSpaceStatistics = () => ["read_only_space", "new_space", "old_space", "code_space", "map_space", "large_object_space", "code_large_object_space", "new_large_object_space"].map(space => getMockHeapSpaceStats(space));
exports.getHeapSpaceStatistics = getHeapSpaceStatistics;
const getHeapStatistics = () => ({
  total_heap_size: 0,
  total_heap_size_executable: 0,
  total_physical_size: 0,
  total_available_size: 0,
  used_heap_size: 0,
  heap_size_limit: 0,
  malloced_memory: 0,
  peak_malloced_memory: 0,
  does_zap_garbage: 0,
  number_of_native_contexts: 0,
  number_of_detached_contexts: 0,
  total_global_handles_size: 0,
  used_global_handles_size: 0,
  external_memory: 0
});
exports.getHeapStatistics = getHeapStatistics;
const getHeapSnapshot = () => {
  return _nodeStream.Readable.from(`{
    snapshot: {},
    nodes: [],
    edges: [],
    trace_function_infos: [],
    trace_tree: [],
    samples: [],
    locations: [],
    strings: [],
  }`);
};
exports.getHeapSnapshot = getHeapSnapshot;
const promiseHooks = exports.promiseHooks = {
  onInit: () => _noop.default,
  onSettled: () => _noop.default,
  onBefore: () => _noop.default,
  onAfter: () => _noop.default,
  createHook: () => _noop.default
};
const serialize = value => Buffer.from(value);
exports.serialize = serialize;
const setFlagsFromString = exports.setFlagsFromString = _noop.default;
const setHeapSnapshotNearHeapLimit = exports.setHeapSnapshotNearHeapLimit = _noop.default;
const startupSnapshot = exports.startupSnapshot = {
  addDeserializeCallback: _noop.default,
  addSerializeCallback: _noop.default,
  setDeserializeMainFunction: _noop.default,
  isBuildingSnapshot: () => false
};
const stopCoverage = exports.stopCoverage = _noop.default;
const takeCoverage = exports.takeCoverage = _noop.default;
const writeHeapSnapshot = () => "";
exports.writeHeapSnapshot = writeHeapSnapshot;
function queryObjects(_ctor, options) {
  if (options?.format === "count") {
    return 0;
  }
  return [];
}
module.exports = {
  DefaultDeserializer: _deserializer.DefaultDeserializer,
  Deserializer: _deserializer.Deserializer,
  GCProfiler: _profiler.GCProfiler,
  DefaultSerializer: _serializer.DefaultSerializer,
  Serializer: _serializer.Serializer,
  cachedDataVersionTag,
  deserialize,
  getHeapCodeStatistics,
  getHeapSnapshot,
  getHeapSpaceStatistics,
  getHeapStatistics,
  promiseHooks,
  serialize,
  setFlagsFromString,
  setHeapSnapshotNearHeapLimit,
  startupSnapshot,
  stopCoverage,
  takeCoverage,
  writeHeapSnapshot,
  queryObjects
};