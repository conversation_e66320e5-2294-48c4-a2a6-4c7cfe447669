import type v8 from "node:v8";
export declare class Serializer implements v8.Serializer {
    writeHeader(): void;
    writeValue(val: any): boolean;
    releaseBuffer(): Buffer;
    transferArrayBuffer(id: number, arrayBuffer: ArrayBuffer): void;
    writeDouble(value: number): void;
    writeUint32(value: number): void;
    writeUint64(hi: number, lo: number): void;
    writeRawBytes(buffer: NodeJS.TypedArray): void;
}
export declare class DefaultSerializer extends Serializer {
}
