import type v8 from "node:v8";
export declare class Deserializer implements v8.Deserializer {
    readHeader(): boolean;
    readValue(): void;
    transferArrayBuffer(id: number, arrayBuffer: ArrayBuffer): void;
    getWireFormatVersion(): number;
    readUint32(): number;
    readUint64(): [number, number];
    readDouble(): number;
    readRawBytes(length: number): Buffer;
}
export declare class DefaultDeserializer extends Deserializer {
}
