"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Serializer = exports.DefaultSerializer = void 0;
class Serializer {
  writeHeader() {}
  writeValue(val) {
    return false;
  }
  releaseBuffer() {
    return Buffer.from("");
  }
  transferArrayBuffer(id, arrayBuffer) {}
  writeDouble(value) {}
  writeUint32(value) {}
  writeUint64(hi, lo) {}
  writeRawBytes(buffer) {}
}
exports.Serializer = Serializer;
class DefaultSerializer extends Serializer {}
exports.DefaultSerializer = DefaultSerializer;