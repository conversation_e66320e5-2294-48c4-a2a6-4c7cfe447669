import type v8 from "node:v8";
export { Deserializer, DefaultDeserializer } from "./internal/deserializer";
export { Serializer, DefaultSerializer } from "./internal/serializer";
export { G<PERSON>rofiler } from "./internal/profiler";
export declare const cachedDataVersionTag: typeof v8.cachedDataVersionTag;
export declare const deserialize: typeof v8.deserialize;
export declare const getHeapCodeStatistics: typeof v8.getHeapCodeStatistics;
export declare const getHeapSpaceStatistics: typeof v8.getHeapSpaceStatistics;
export declare const getHeapStatistics: typeof v8.getHeapStatistics;
export declare const getHeapSnapshot: typeof v8.getHeapSnapshot;
export declare const promiseHooks: typeof v8.promiseHooks;
export declare const serialize: typeof v8.serialize;
export declare const setFlagsFromString: typeof v8.setFlagsFromString;
export declare const setHeapSnapshotNearHeapLimit: typeof v8.setHeapSnapshotNearHeapLimit;
export declare const startupSnapshot: typeof v8.startupSnapshot;
export declare const stopCoverage: typeof v8.stopCoverage;
export declare const takeCoverage: typeof v8.takeCoverage;
export declare const writeHeapSnapshot: typeof v8.writeHeapSnapshot;
type _Function = Function;
export declare function queryObjects(ctor: _Function): number | string[];
export declare function queryObjects(ctor: _Function, options: {
    format: "count";
}): number;
export declare function queryObjects(ctor: _Function, options: {
    format: "summary";
}): string[];
declare const _default: typeof v8;
export default _default;
