import { notImplemented, notImplementedClass } from "../../_internal/utils.mjs";
import mock from "../../mock/proxy.mjs";
import * as consts from "./internal/consts.mjs";
import { IncomingMessage } from "./internal/request.mjs";
import { ServerResponse } from "./internal/response.mjs";
export * from "./internal/consts.mjs";
export * from "./internal/request.mjs";
export * from "./internal/response.mjs";
export const createServer = notImplemented("http.createServer");
export const request = notImplemented("http.request");
export const get = notImplemented("http.get");
export const Server = mock.__createMock__("http.Server");
export const OutgoingMessage = mock.__createMock__(
  "http.OutgoingMessage"
);
export const ClientRequest = mock.__createMock__("http.ClientRequest");
export const Agent = mock.__createMock__("http.Agent");
export const globalAgent = new Agent();
export const validateHeaderName = notImplemented("http.validateHeaderName");
export const validateHeaderValue = notImplemented("http.validateHeaderValue");
export const setMaxIdleHTTPParsers = notImplemented("http.setMaxIdleHTTPParsers");
export const _connectionListener = notImplemented("http._connectionListener");
export const WebSocket = globalThis.WebSocket || notImplementedClass("WebSocket");
export const CloseEvent = globalThis.CloseEvent || notImplementedClass("CloseEvent");
export const MessageEvent = globalThis.MessageEvent || notImplementedClass("MessageEvent");
export default {
  ...consts,
  IncomingMessage,
  ServerResponse,
  WebSocket,
  CloseEvent,
  MessageEvent,
  createServer,
  request,
  get,
  Server,
  OutgoingMessage,
  ClientRequest,
  Agent,
  globalAgent,
  validateHeaderName,
  validateHeaderValue,
  setMaxIdleHTTPParsers,
  _connectionListener
};
