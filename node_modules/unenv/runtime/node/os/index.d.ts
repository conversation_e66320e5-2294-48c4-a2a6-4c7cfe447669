import type os from "node:os";
export { constants } from "./internal/constants";
export declare const availableParallelism: typeof os.availableParallelism;
export declare const arch: typeof os.arch;
export declare const machine: typeof os.machine;
export declare const endianness: typeof os.endianness;
export declare const cpus: typeof os.cpus;
export declare const getPriority: typeof os.getPriority;
export declare const setPriority: typeof os.setPriority;
export declare const homedir: typeof os.homedir;
export declare const tmpdir: typeof os.tmpdir;
export declare const devNull: typeof os.devNull;
export declare const freemem: typeof os.freemem;
export declare const totalmem: typeof os.totalmem;
export declare const loadavg: typeof os.loadavg;
export declare const uptime: typeof os.uptime;
export declare const hostname: typeof os.hostname;
export declare const networkInterfaces: typeof os.networkInterfaces;
export declare const platform: typeof os.platform;
export declare const type: typeof os.type;
export declare const release: typeof os.release;
export declare const version: typeof os.version;
export declare const userInfo: typeof os.userInfo;
export declare const EOL: typeof os.EOL;
declare const _default: typeof os;
export default _default;
