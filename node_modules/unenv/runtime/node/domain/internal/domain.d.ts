import { EventEmitter } from "node:events";
import type domain from "node:domain";
export declare class Domain extends EventEmitter implements domain.Domain {
    readonly __unenv__ = true;
    members: never[];
    add(): void;
    enter(): void;
    exit(): void;
    remove(): void;
    bind<T>(callback: T): T;
    intercept<T>(callback: T): T;
    run<T>(fn: (...args: any[]) => T, ...args: any[]): T;
}
