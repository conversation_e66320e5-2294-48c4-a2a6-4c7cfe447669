import type perf_hooks from "node:perf_hooks";
declare class Histogram implements perf_hooks.Histogram {
    min: number;
    max: number;
    mean: number;
    exceeds: number;
    stddev: number;
    count: number;
    countBigInt: bigint;
    exceedsBigInt: bigint;
    maxBigInt: number;
    minBigInt: bigint;
    percentiles: Map<number, number>;
    percentilesBigInt: Map<bigint, bigint>;
    percentileBigInt(_percentile: number): bigint;
    percentile(percentile: number): number;
    reset(): void;
}
export declare class IntervalHistogram extends Histogram implements perf_hooks.IntervalHistogram {
    enable(): boolean;
    disable(): boolean;
}
export declare class RecordableHistogram extends Histogram implements perf_hooks.RecordableHistogram {
    record(val: number | bigint): void;
    recordDelta(): void;
    add(other: perf_hooks.RecordableHistogram): void;
}
export {};
