import type perf_hooks from "node:perf_hooks";
export { PerformanceResourceTiming, PerformanceObserverEntryList, } from "../../../web/performance/index";
export declare const Performance: {
    new (): {
        timerify<T extends (...params: any[]) => any>(_fn: T, _options?: perf_hooks.TimerifyOptions | undefined): T;
        readonly nodeTiming: perf_hooks.PerformanceNodeTiming;
        eventLoopUtilization(): perf_hooks.EventLoopUtilization;
        mark(name: string, options?: PerformanceMarkOptions | undefined): any;
        measure(measureName: string, startOrMeasureOptions?: string | PerformanceMeasureOptions | undefined, endMark?: string | undefined): any;
        markResourceTiming(timingInfo: object, requestedUrl: string, initiatorType: string, global: object, cacheMode: "" | "local", bodyInfo: object, responseStatus: number, deliveryType?: string): perf_hooks.PerformanceResourceTiming;
        readonly __unenv__: true;
        timeOrigin: number;
        eventCounts: EventCounts;
        _entries: PerformanceEntry[];
        _resourceTimingBufferSize: number;
        navigation: any;
        timing: any;
        onresourcetimingbufferfull: ((this: Performance, ev: Event) => any) | null;
        now(): number;
        clearMarks(markName?: string | undefined): void;
        clearMeasures(measureName?: string | undefined): void;
        clearResourceTimings(): void;
        getEntries(): perf_hooks.PerformanceEntry[];
        getEntriesByName(name: string, type?: string | undefined): perf_hooks.PerformanceEntry[];
        getEntriesByType(type: string): perf_hooks.PerformanceEntry[];
        setResourceTimingBufferSize(maxSize: number): void;
        toJSON(): /*elided*/ any;
        addEventListener<K extends "resourcetimingbufferfull">(type: K, listener: (this: Performance, ev: PerformanceEventMap[K]) => any, options?: boolean | AddEventListenerOptions | undefined): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions | undefined): void;
        removeEventListener<K extends "resourcetimingbufferfull">(type: K, listener: (this: Performance, ev: PerformanceEventMap[K]) => any, options?: boolean | EventListenerOptions | undefined): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions | undefined): void;
        dispatchEvent(event: Event): boolean;
    };
};
export declare const performance: perf_hooks.Performance;
export declare const PerformanceMark: typeof perf_hooks.PerformanceMark;
export declare const PerformanceEntry: typeof perf_hooks.PerformanceEntry;
export declare const PerformanceMeasure: typeof perf_hooks.PerformanceMeasure;
export declare const PerformanceObserver: typeof perf_hooks.PerformanceObserver;
