import { createNotImplementedError } from "../../../_internal/utils.mjs";
import {
  _Performance,
  _PerformanceEntry,
  _PerformanceMark,
  _PerformanceMeasure,
  _PerformanceObserver,
  _PerformanceResourceTiming
} from "../../../web/performance/index.mjs";
export {
  PerformanceResourceTiming,
  PerformanceObserverEntryList
} from "../../../web/performance/index.mjs";
const nodeTiming = {
  name: "node",
  entryType: "node",
  startTime: 0,
  duration: 0,
  nodeStart: 0,
  v8Start: 0,
  bootstrapComplete: 0,
  environment: 0,
  loopStart: 0,
  loopExit: 0,
  idleTime: 0,
  uvMetricsInfo: { loopCount: 0, events: 0, eventsWaiting: 0 },
  // only present in Node.js 18.x
  detail: void 0
};
export const Performance = class Performance2 extends _Performance {
  constructor() {
    super();
  }
  timerify(_fn, _options) {
    throw createNotImplementedError("Performance.timerify");
  }
  get nodeTiming() {
    return {
      ...nodeTiming,
      toJSON: () => nodeTiming
    };
  }
  eventLoopUtilization() {
    return {};
  }
  mark(name, options) {
    const entry = super.mark(name, options);
    return entry;
  }
  measure(measureName, startOrMeasureOptions, endMark) {
    const entry = super.measure(measureName, startOrMeasureOptions, endMark);
    return entry;
  }
  markResourceTiming(timingInfo, requestedUrl, initiatorType, global, cacheMode, bodyInfo, responseStatus, deliveryType) {
    return new _PerformanceResourceTiming("");
  }
};
export const performance = globalThis.performance ?? new Performance();
export const PerformanceMark = class PerformanceMark2 extends _PerformanceMark {
  constructor() {
    super(...arguments);
  }
  get duration() {
    return 0;
  }
};
export const PerformanceEntry = class PerformanceEntry2 extends _PerformanceEntry {
  entryType = "event";
  constructor() {
    super(...arguments);
  }
};
export const PerformanceMeasure = class PerformanceMeasure2 extends _PerformanceMeasure {
  constructor() {
    super(...arguments);
  }
};
export const PerformanceObserver = class PerformanceObserver2 extends _PerformanceObserver {
  constructor(callback) {
    super(callback);
  }
  observe(options) {
    throw createNotImplementedError("PerformanceObserver.observe");
  }
  bind(fn) {
    return fn;
  }
  runInAsyncScope(fn, thisArg, ...args) {
    return fn.call(thisArg, ...args);
  }
  asyncId() {
    return 0;
  }
  triggerAsyncId() {
    return 0;
  }
  emitDestroy() {
    return this;
  }
};
