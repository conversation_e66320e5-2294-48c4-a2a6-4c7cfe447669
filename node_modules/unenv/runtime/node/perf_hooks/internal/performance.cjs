"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PerformanceObserver = exports.PerformanceMeasure = exports.PerformanceMark = exports.PerformanceEntry = exports.Performance = void 0;
Object.defineProperty(exports, "PerformanceObserverEntryList", {
  enumerable: true,
  get: function () {
    return _index.PerformanceObserverEntryList;
  }
});
Object.defineProperty(exports, "PerformanceResourceTiming", {
  enumerable: true,
  get: function () {
    return _index.PerformanceResourceTiming;
  }
});
exports.performance = void 0;
var _utils = require("../../../_internal/utils.cjs");
var _index = require("../../../web/performance/index.cjs");
const nodeTiming = {
  name: "node",
  entryType: "node",
  startTime: 0,
  duration: 0,
  nodeStart: 0,
  v8Start: 0,
  bootstrapComplete: 0,
  environment: 0,
  loopStart: 0,
  loopExit: 0,
  idleTime: 0,
  uvMetricsInfo: {
    loopCount: 0,
    events: 0,
    eventsWaiting: 0
  },
  // only present in Node.js 18.x
  detail: void 0
};
const Performance = exports.Performance = class Performance2 extends _index._Performance {
  constructor() {
    super();
  }
  timerify(_fn, _options) {
    throw (0, _utils.createNotImplementedError)("Performance.timerify");
  }
  get nodeTiming() {
    return {
      ...nodeTiming,
      toJSON: () => nodeTiming
    };
  }
  eventLoopUtilization() {
    return {};
  }
  mark(name, options) {
    const entry = super.mark(name, options);
    return entry;
  }
  measure(measureName, startOrMeasureOptions, endMark) {
    const entry = super.measure(measureName, startOrMeasureOptions, endMark);
    return entry;
  }
  markResourceTiming(timingInfo, requestedUrl, initiatorType, global, cacheMode, bodyInfo, responseStatus, deliveryType) {
    return new _index._PerformanceResourceTiming("");
  }
};
const performance = exports.performance = globalThis.performance ?? new Performance();
const PerformanceMark = exports.PerformanceMark = class PerformanceMark2 extends _index._PerformanceMark {
  constructor() {
    super(...arguments);
  }
  get duration() {
    return 0;
  }
};
const PerformanceEntry = exports.PerformanceEntry = class PerformanceEntry2 extends _index._PerformanceEntry {
  entryType = "event";
  constructor() {
    super(...arguments);
  }
};
const PerformanceMeasure = exports.PerformanceMeasure = class PerformanceMeasure2 extends _index._PerformanceMeasure {
  constructor() {
    super(...arguments);
  }
};
const PerformanceObserver = exports.PerformanceObserver = class PerformanceObserver2 extends _index._PerformanceObserver {
  constructor(callback) {
    super(callback);
  }
  observe(options) {
    throw (0, _utils.createNotImplementedError)("PerformanceObserver.observe");
  }
  bind(fn) {
    return fn;
  }
  runInAsyncScope(fn, thisArg, ...args) {
    return fn.call(thisArg, ...args);
  }
  asyncId() {
    return 0;
  }
  triggerAsyncId() {
    return 0;
  }
  emitDestroy() {
    return this;
  }
};