import { notImplemented } from "../../../_internal/utils.mjs";
export const arrayBuffer = notImplemented("stream.consumers.arrayBuffer");
export const blob = notImplemented("stream.consumers.blob");
export const buffer = notImplemented("stream.consumers.buffer");
export const text = notImplemented("stream.consumers.text");
export const json = notImplemented("stream.consumers.json");
export default {
  arrayBuffer,
  blob,
  buffer,
  text,
  json
};
