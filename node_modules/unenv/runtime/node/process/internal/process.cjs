"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.emitWarning = exports.emit = exports.domain = exports.dlopen = exports.debugPort = exports.cwd = exports.cpuUsage = exports.constrainedMemory = exports.config = exports.chdir = exports.binding = exports.availableMemory = exports.assert = exports.argv0 = exports.argv = exports.arch = exports.allowedNodeEnvironmentFlags = exports.addListener = exports.abort = exports._tickCallback = exports._stopProfilerIdleNotifier = exports._startProfilerIdleNotifier = exports._rawDebug = exports._preload_modules = exports._maxListeners = exports._linkedBinding = exports._kill = exports._getActiveRequests = exports._getActiveHandles = exports._fatalException = exports._exiting = exports._eventsCount = exports._events = exports._debugProcess = exports._debugEnd = void 0;
Object.defineProperty(exports, "env", {
  enumerable: true,
  get: function () {
    return _env.env;
  }
});
exports.hasUncaughtExceptionCaptureCallback = exports.getuid = exports.getgroups = exports.getgid = exports.geteuid = exports.getegid = exports.getMaxListeners = exports.getBuiltinModule = exports.getActiveResourcesInfo = exports.finalization = exports.features = exports.exitCode = exports.exit = exports.execPath = exports.execArgv = exports.eventNames = void 0;
Object.defineProperty(exports, "hrtime", {
  enumerable: true,
  get: function () {
    return _time.hrtime;
  }
});
exports.moduleLoadList = exports.memoryUsage = exports.loadEnvFile = exports.listeners = exports.listenerCount = exports.kill = exports.initgroups = void 0;
Object.defineProperty(exports, "nextTick", {
  enumerable: true,
  get: function () {
    return _time.nextTick;
  }
});
exports.versions = exports.version = exports.uptime = exports.umask = exports.title = exports.stdout = exports.stdin = exports.stderr = exports.sourceMapsEnabled = exports.setuid = exports.setgroups = exports.setgid = exports.seteuid = exports.setegid = exports.setUncaughtExceptionCaptureCallback = exports.setSourceMapsEnabled = exports.setMaxListeners = exports.resourceUsage = exports.report = exports.removeListener = exports.removeAllListeners = exports.release = exports.reallyExit = exports.rawListeners = exports.process = exports.prependOnceListener = exports.prependListener = exports.ppid = exports.platform = exports.pid = exports.openStdin = exports.once = exports.on = exports.off = void 0;
var _nodeTty = require("node:tty");
var _empty = _interopRequireDefault(require("../../../mock/empty.cjs"));
var _utils = require("../../../_internal/utils.cjs");
var _env = require("./env.cjs");
var _time = require("./time.cjs");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const title = exports.title = "unenv";
const argv = exports.argv = [];
const version = exports.version = "";
const versions = exports.versions = {
  ares: "",
  http_parser: "",
  icu: "",
  modules: "",
  node: "",
  openssl: "",
  uv: "",
  v8: "",
  zlib: ""
};
function noop() {
  return process;
}
const on = exports.on = noop;
const addListener = exports.addListener = noop;
const once = exports.once = noop;
const off = exports.off = noop;
const removeListener = exports.removeListener = noop;
const removeAllListeners = exports.removeAllListeners = noop;
const emit = exports.emit = function emit2(event) {
  if (event === "message" || event === "multipleResolves") {
    return process;
  }
  return false;
};
const prependListener = exports.prependListener = noop;
const prependOnceListener = exports.prependOnceListener = noop;
const listeners = function (name) {
  return [];
};
exports.listeners = listeners;
const listenerCount = () => 0;
exports.listenerCount = listenerCount;
const binding = function (name) {
  throw new Error("[unenv] process.binding is not supported");
};
exports.binding = binding;
let _cwd = "/";
const cwd = exports.cwd = function cwd2() {
  return _cwd;
};
const chdir = exports.chdir = function chdir2(dir) {
  _cwd = dir;
};
const umask = exports.umask = function umask2() {
  return 0;
};
const getegid = exports.getegid = function getegid2() {
  return 1e3;
};
const geteuid = exports.geteuid = function geteuid2() {
  return 1e3;
};
const getgid = exports.getgid = function getgid2() {
  return 1e3;
};
const getuid = exports.getuid = function getuid2() {
  return 1e3;
};
const getgroups = exports.getgroups = function getgroups2() {
  return [];
};
const getBuiltinModule = _name => void 0;
exports.getBuiltinModule = getBuiltinModule;
const abort = exports.abort = (0, _utils.notImplemented)("process.abort");
const allowedNodeEnvironmentFlags = exports.allowedNodeEnvironmentFlags = /* @__PURE__ */new Set();
const arch = exports.arch = "";
const argv0 = exports.argv0 = "";
const config = exports.config = _empty.default;
const connected = false;
const constrainedMemory = () => 0;
exports.constrainedMemory = constrainedMemory;
const availableMemory = () => 0;
exports.availableMemory = availableMemory;
const cpuUsage = exports.cpuUsage = (0, _utils.notImplemented)("process.cpuUsage");
const debugPort = exports.debugPort = 0;
const dlopen = exports.dlopen = (0, _utils.notImplemented)("process.dlopen");
const disconnect = noop;
const emitWarning = exports.emitWarning = noop;
const eventNames = exports.eventNames = (0, _utils.notImplemented)("process.eventNames");
const execArgv = exports.execArgv = [];
const execPath = exports.execPath = "";
const exit = exports.exit = (0, _utils.notImplemented)("process.exit");
const features = exports.features = /* @__PURE__ */Object.create({
  inspector: void 0,
  debug: void 0,
  uv: void 0,
  ipv6: void 0,
  tls_alpn: void 0,
  tls_sni: void 0,
  tls_ocsp: void 0,
  tls: void 0,
  cached_builtins: void 0
});
const getActiveResourcesInfo = () => [];
exports.getActiveResourcesInfo = getActiveResourcesInfo;
const getMaxListeners = exports.getMaxListeners = (0, _utils.notImplemented)("process.getMaxListeners");
const kill = exports.kill = (0, _utils.notImplemented)("process.kill");
const memoryUsage = exports.memoryUsage = Object.assign(() => ({
  arrayBuffers: 0,
  rss: 0,
  external: 0,
  heapTotal: 0,
  heapUsed: 0
}), {
  rss: () => 0
});
const pid = exports.pid = 1e3;
const platform = exports.platform = "";
const ppid = exports.ppid = 1e3;
const rawListeners = exports.rawListeners = (0, _utils.notImplemented)("process.rawListeners");
const release = exports.release = /* @__PURE__ */Object.create({
  name: "",
  lts: "",
  sourceUrl: void 0,
  headersUrl: void 0
});
const report = exports.report = /* @__PURE__ */Object.create({
  compact: void 0,
  directory: void 0,
  filename: void 0,
  getReport: (0, _utils.notImplemented)("process.report.getReport"),
  reportOnFatalError: void 0,
  reportOnSignal: void 0,
  reportOnUncaughtException: void 0,
  signal: void 0,
  writeReport: (0, _utils.notImplemented)("process.report.writeReport")
});
const resourceUsage = exports.resourceUsage = (0, _utils.notImplemented)("process.resourceUsage");
const setegid = exports.setegid = (0, _utils.notImplemented)("process.setegid");
const seteuid = exports.seteuid = (0, _utils.notImplemented)("process.seteuid");
const setgid = exports.setgid = (0, _utils.notImplemented)("process.setgid");
const setgroups = exports.setgroups = (0, _utils.notImplemented)("process.setgroups");
const setuid = exports.setuid = (0, _utils.notImplemented)("process.setuid");
const setMaxListeners = exports.setMaxListeners = (0, _utils.notImplemented)("process.setMaxListeners");
const setSourceMapsEnabled = exports.setSourceMapsEnabled = (0, _utils.notImplemented)("process.setSourceMapsEnabled");
const stdin = exports.stdin = new _nodeTty.ReadStream(0);
const stdout = exports.stdout = new _nodeTty.WriteStream(1);
const stderr = exports.stderr = new _nodeTty.WriteStream(2);
const traceDeprecation = false;
const uptime = () => 0;
exports.uptime = uptime;
const exitCode = exports.exitCode = 0;
const setUncaughtExceptionCaptureCallback = exports.setUncaughtExceptionCaptureCallback = (0, _utils.notImplemented)("process.setUncaughtExceptionCaptureCallback");
const hasUncaughtExceptionCaptureCallback = () => false;
exports.hasUncaughtExceptionCaptureCallback = hasUncaughtExceptionCaptureCallback;
const sourceMapsEnabled = exports.sourceMapsEnabled = false;
const loadEnvFile = exports.loadEnvFile = (0, _utils.notImplemented)("process.loadEnvFile");
const mainModule = void 0;
const permission = {
  has: () => false
};
const channel = {
  ref() {},
  unref() {}
};
const throwDeprecation = false;
const finalization = exports.finalization = {
  register() {},
  unregister() {},
  registerBeforeExit() {}
};
const assert = exports.assert = (0, _utils.notImplemented)("process.assert");
const openStdin = exports.openStdin = (0, _utils.notImplemented)("process.openStdin");
const _debugEnd = exports._debugEnd = (0, _utils.notImplemented)("process._debugEnd");
const _debugProcess = exports._debugProcess = (0, _utils.notImplemented)("process._debugProcess");
const _fatalException = exports._fatalException = (0, _utils.notImplemented)("process._fatalException");
const _getActiveHandles = exports._getActiveHandles = (0, _utils.notImplemented)("process._getActiveHandles");
const _getActiveRequests = exports._getActiveRequests = (0, _utils.notImplemented)("process._getActiveRequests");
const _kill = exports._kill = (0, _utils.notImplemented)("process._kill");
const _preload_modules = exports._preload_modules = [];
const _rawDebug = exports._rawDebug = (0, _utils.notImplemented)("process._rawDebug");
const _startProfilerIdleNotifier = exports._startProfilerIdleNotifier = (0, _utils.notImplemented)("process._startProfilerIdleNotifier");
const _stopProfilerIdleNotifier = exports._stopProfilerIdleNotifier = (0, _utils.notImplemented)("process.__stopProfilerIdleNotifier");
const _tickCallback = exports._tickCallback = (0, _utils.notImplemented)("process._tickCallback");
const _linkedBinding = exports._linkedBinding = (0, _utils.notImplemented)("process._linkedBinding");
const domain = exports.domain = void 0;
const initgroups = exports.initgroups = (0, _utils.notImplemented)("process.initgroups");
const moduleLoadList = exports.moduleLoadList = [];
const reallyExit = exports.reallyExit = noop;
const _exiting = exports._exiting = false;
const _events = exports._events = [];
const _eventsCount = exports._eventsCount = 0;
const _maxListeners = exports._maxListeners = 0;
const process = exports.process = {
  // @ts-expect-error
  _events,
  _eventsCount,
  _exiting,
  _maxListeners,
  _debugEnd,
  _debugProcess,
  _fatalException,
  _getActiveHandles,
  _getActiveRequests,
  _kill,
  _preload_modules,
  _rawDebug,
  _startProfilerIdleNotifier,
  _stopProfilerIdleNotifier,
  _tickCallback,
  domain,
  initgroups,
  moduleLoadList,
  reallyExit,
  exitCode,
  abort,
  addListener,
  allowedNodeEnvironmentFlags,
  hasUncaughtExceptionCaptureCallback,
  setUncaughtExceptionCaptureCallback,
  loadEnvFile,
  sourceMapsEnabled,
  throwDeprecation,
  mainModule,
  permission,
  channel,
  arch,
  argv,
  argv0,
  assert,
  binding,
  chdir,
  config,
  connected,
  constrainedMemory,
  availableMemory,
  cpuUsage,
  cwd,
  debugPort,
  dlopen,
  disconnect,
  emit,
  emitWarning,
  env: _env.env,
  eventNames,
  execArgv,
  execPath,
  exit,
  finalization,
  features,
  getBuiltinModule,
  getegid,
  geteuid,
  getgid,
  getgroups,
  getuid,
  getActiveResourcesInfo,
  getMaxListeners,
  hrtime: _time.hrtime,
  kill,
  listeners,
  listenerCount,
  memoryUsage,
  nextTick: _time.nextTick,
  on,
  off,
  once,
  openStdin,
  pid,
  platform,
  ppid,
  prependListener,
  prependOnceListener,
  rawListeners,
  release,
  removeAllListeners,
  removeListener,
  report,
  resourceUsage,
  setegid,
  seteuid,
  setgid,
  setgroups,
  setuid,
  setMaxListeners,
  setSourceMapsEnabled,
  stderr,
  stdin,
  stdout,
  title,
  traceDeprecation,
  umask,
  uptime,
  version,
  versions
};