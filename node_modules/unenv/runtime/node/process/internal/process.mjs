import { ReadStream, WriteStream } from "node:tty";
import empty from "../../../mock/empty.mjs";
import { notImplemented } from "../../../_internal/utils.mjs";
import { env } from "./env.mjs";
import { hrtime, nextTick } from "./time.mjs";
export { hrtime, nextTick } from "./time.mjs";
export { env } from "./env.mjs";
export const title = "unenv";
export const argv = [];
export const version = "";
export const versions = {
  ares: "",
  http_parser: "",
  icu: "",
  modules: "",
  node: "",
  openssl: "",
  uv: "",
  v8: "",
  zlib: ""
};
function noop() {
  return process;
}
export const on = noop;
export const addListener = noop;
export const once = noop;
export const off = noop;
export const removeListener = noop;
export const removeAllListeners = noop;
export const emit = function emit2(event) {
  if (event === "message" || event === "multipleResolves") {
    return process;
  }
  return false;
};
export const prependListener = noop;
export const prependOnceListener = noop;
export const listeners = function(name) {
  return [];
};
export const listenerCount = () => 0;
export const binding = function(name) {
  throw new Error("[unenv] process.binding is not supported");
};
let _cwd = "/";
export const cwd = function cwd2() {
  return _cwd;
};
export const chdir = function chdir2(dir) {
  _cwd = dir;
};
export const umask = function umask2() {
  return 0;
};
export const getegid = function getegid2() {
  return 1e3;
};
export const geteuid = function geteuid2() {
  return 1e3;
};
export const getgid = function getgid2() {
  return 1e3;
};
export const getuid = function getuid2() {
  return 1e3;
};
export const getgroups = function getgroups2() {
  return [];
};
export const getBuiltinModule = (_name) => void 0;
export const abort = notImplemented("process.abort");
export const allowedNodeEnvironmentFlags = /* @__PURE__ */ new Set();
export const arch = "";
export const argv0 = "";
export const config = empty;
const connected = false;
export const constrainedMemory = () => 0;
export const availableMemory = () => 0;
export const cpuUsage = notImplemented("process.cpuUsage");
export const debugPort = 0;
export const dlopen = notImplemented("process.dlopen");
const disconnect = noop;
export const emitWarning = noop;
export const eventNames = notImplemented("process.eventNames");
export const execArgv = [];
export const execPath = "";
export const exit = notImplemented("process.exit");
export const features = /* @__PURE__ */ Object.create({
  inspector: void 0,
  debug: void 0,
  uv: void 0,
  ipv6: void 0,
  tls_alpn: void 0,
  tls_sni: void 0,
  tls_ocsp: void 0,
  tls: void 0,
  cached_builtins: void 0
});
export const getActiveResourcesInfo = () => [];
export const getMaxListeners = notImplemented(
  "process.getMaxListeners"
);
export const kill = notImplemented("process.kill");
export const memoryUsage = Object.assign(
  () => ({
    arrayBuffers: 0,
    rss: 0,
    external: 0,
    heapTotal: 0,
    heapUsed: 0
  }),
  { rss: () => 0 }
);
export const pid = 1e3;
export const platform = "";
export const ppid = 1e3;
export const rawListeners = notImplemented(
  "process.rawListeners"
);
export const release = /* @__PURE__ */ Object.create({
  name: "",
  lts: "",
  sourceUrl: void 0,
  headersUrl: void 0
});
export const report = /* @__PURE__ */ Object.create({
  compact: void 0,
  directory: void 0,
  filename: void 0,
  getReport: notImplemented("process.report.getReport"),
  reportOnFatalError: void 0,
  reportOnSignal: void 0,
  reportOnUncaughtException: void 0,
  signal: void 0,
  writeReport: notImplemented("process.report.writeReport")
});
export const resourceUsage = notImplemented(
  "process.resourceUsage"
);
export const setegid = notImplemented("process.setegid");
export const seteuid = notImplemented("process.seteuid");
export const setgid = notImplemented("process.setgid");
export const setgroups = notImplemented("process.setgroups");
export const setuid = notImplemented("process.setuid");
export const setMaxListeners = notImplemented(
  "process.setMaxListeners"
);
export const setSourceMapsEnabled = notImplemented("process.setSourceMapsEnabled");
export const stdin = new ReadStream(0);
export const stdout = new WriteStream(1);
export const stderr = new WriteStream(2);
const traceDeprecation = false;
export const uptime = () => 0;
export const exitCode = 0;
export const setUncaughtExceptionCaptureCallback = notImplemented("process.setUncaughtExceptionCaptureCallback");
export const hasUncaughtExceptionCaptureCallback = () => false;
export const sourceMapsEnabled = false;
export const loadEnvFile = notImplemented(
  "process.loadEnvFile"
);
const mainModule = void 0;
const permission = {
  has: () => false
};
const channel = {
  ref() {
  },
  unref() {
  }
};
const throwDeprecation = false;
export const finalization = {
  register() {
  },
  unregister() {
  },
  registerBeforeExit() {
  }
};
export const assert = notImplemented("process.assert");
export const openStdin = notImplemented("process.openStdin");
export const _debugEnd = notImplemented("process._debugEnd");
export const _debugProcess = notImplemented("process._debugProcess");
export const _fatalException = notImplemented("process._fatalException");
export const _getActiveHandles = notImplemented("process._getActiveHandles");
export const _getActiveRequests = notImplemented("process._getActiveRequests");
export const _kill = notImplemented("process._kill");
export const _preload_modules = [];
export const _rawDebug = notImplemented("process._rawDebug");
export const _startProfilerIdleNotifier = notImplemented(
  "process._startProfilerIdleNotifier"
);
export const _stopProfilerIdleNotifier = notImplemented(
  "process.__stopProfilerIdleNotifier"
);
export const _tickCallback = notImplemented("process._tickCallback");
export const _linkedBinding = notImplemented("process._linkedBinding");
export const domain = void 0;
export const initgroups = notImplemented("process.initgroups");
export const moduleLoadList = [];
export const reallyExit = noop;
export const _exiting = false;
export const _events = [];
export const _eventsCount = 0;
export const _maxListeners = 0;
export const process = {
  // @ts-expect-error
  _events,
  _eventsCount,
  _exiting,
  _maxListeners,
  _debugEnd,
  _debugProcess,
  _fatalException,
  _getActiveHandles,
  _getActiveRequests,
  _kill,
  _preload_modules,
  _rawDebug,
  _startProfilerIdleNotifier,
  _stopProfilerIdleNotifier,
  _tickCallback,
  domain,
  initgroups,
  moduleLoadList,
  reallyExit,
  exitCode,
  abort,
  addListener,
  allowedNodeEnvironmentFlags,
  hasUncaughtExceptionCaptureCallback,
  setUncaughtExceptionCaptureCallback,
  loadEnvFile,
  sourceMapsEnabled,
  throwDeprecation,
  mainModule,
  permission,
  channel,
  arch,
  argv,
  argv0,
  assert,
  binding,
  chdir,
  config,
  connected,
  constrainedMemory,
  availableMemory,
  cpuUsage,
  cwd,
  debugPort,
  dlopen,
  disconnect,
  emit,
  emitWarning,
  env,
  eventNames,
  execArgv,
  execPath,
  exit,
  finalization,
  features,
  getBuiltinModule,
  getegid,
  geteuid,
  getgid,
  getgroups,
  getuid,
  getActiveResourcesInfo,
  getMaxListeners,
  hrtime,
  kill,
  listeners,
  listenerCount,
  memoryUsage,
  nextTick,
  on,
  off,
  once,
  openStdin,
  pid,
  platform,
  ppid,
  prependListener,
  prependOnceListener,
  rawListeners,
  release,
  removeAllListeners,
  removeListener,
  report,
  resourceUsage,
  setegid,
  seteuid,
  setgid,
  setgroups,
  setuid,
  setMaxListeners,
  setSourceMapsEnabled,
  stderr,
  stdin,
  stdout,
  title,
  traceDeprecation,
  umask,
  uptime,
  version,
  versions
};
