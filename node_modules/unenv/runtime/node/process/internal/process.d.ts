export { hrtime, nextTick } from "./time";
export { env } from "./env";
type Process = NodeJS.Process;
export declare const title: Process["title"];
export declare const argv: Process["argv"];
export declare const version: Process["version"];
export declare const versions: Process["versions"];
declare function noop(): Process;
export declare const on: Process["on"];
export declare const addListener: Process["addListener"];
export declare const once: Process["once"];
export declare const off: Process["off"];
export declare const removeListener: Process["removeListener"];
export declare const removeAllListeners: Process["removeAllListeners"];
export declare const emit: Process["emit"];
export declare const prependListener: Process["prependListener"];
export declare const prependOnceListener: Process["prependOnceListener"];
export declare const listeners: Process["listeners"];
export declare const listenerCount: Process["listenerCount"];
export declare const binding: Process["binding"];
export declare const cwd: Process["cwd"];
export declare const chdir: Process["chdir"];
export declare const umask: Process["umask"];
export declare const getegid: Process["getegid"];
export declare const geteuid: Process["geteuid"];
export declare const getgid: Process["getgid"];
export declare const getuid: Process["getuid"];
export declare const getgroups: Process["getgroups"];
export declare const getBuiltinModule: (_name: string) => undefined;
export declare const abort: () => never;
export declare const allowedNodeEnvironmentFlags: Process["allowedNodeEnvironmentFlags"];
export declare const arch: Process["arch"];
export declare const argv0: Process["argv0"];
export declare const config: Process["config"];
export declare const constrainedMemory: Process["constrainedMemory"];
export declare const availableMemory: Process["availableMemory"];
export declare const cpuUsage: (previousValue?: NodeJS.CpuUsage) => NodeJS.CpuUsage;
export declare const debugPort: Process["debugPort"];
export declare const dlopen: (module: object, filename: string, flags?: number) => void;
export declare const emitWarning: Process["emitWarning"];
export declare const eventNames: () => (string | symbol)[];
export declare const execArgv: Process["execArgv"];
export declare const execPath: Process["execPath"];
export declare const exit: (code?: number | string | null | undefined) => never;
export declare const features: Process["features"];
export declare const getActiveResourcesInfo: Process["getActiveResourcesInfo"];
export declare const getMaxListeners: () => number;
export declare const kill: (pid: number, signal?: string | number) => true;
export declare const memoryUsage: Process["memoryUsage"];
export declare const pid: Process["pid"];
export declare const platform: Process["platform"];
export declare const ppid: Process["ppid"];
export declare const rawListeners: <K>(eventName: string | symbol) => Function[];
export declare const release: Process["release"];
export declare const report: Exclude<Process["report"], undefined>;
export declare const resourceUsage: () => NodeJS.ResourceUsage;
export declare const setegid: (id: number | string) => void;
export declare const seteuid: (id: number | string) => void;
export declare const setgid: (id: number | string) => void;
export declare const setgroups: (groups: ReadonlyArray<string | number>) => void;
export declare const setuid: (id: number | string) => void;
export declare const setMaxListeners: (n: number) => NodeJS.Process;
export declare const setSourceMapsEnabled: (value: boolean) => void;
export declare const stdin: Process["stdin"];
export declare const stdout: Process["stdout"];
export declare const stderr: Process["stderr"];
export declare const uptime: Process["uptime"];
export declare const exitCode: Process["exitCode"];
export declare const setUncaughtExceptionCaptureCallback: (cb: ((err: Error) => void) | null) => void;
export declare const hasUncaughtExceptionCaptureCallback: Process["hasUncaughtExceptionCaptureCallback"];
export declare const sourceMapsEnabled: Process["sourceMapsEnabled"];
export declare const loadEnvFile: (path?: string | URL | Buffer) => void;
export declare const finalization: Process["finalization"];
export declare const assert: (...args: any) => any;
export declare const openStdin: (...args: any) => any;
export declare const _debugEnd: (...args: any) => any;
export declare const _debugProcess: (...args: any) => any;
export declare const _fatalException: (...args: any) => any;
export declare const _getActiveHandles: (...args: any) => any;
export declare const _getActiveRequests: (...args: any) => any;
export declare const _kill: (...args: any) => any;
export declare const _preload_modules: string[];
export declare const _rawDebug: (...args: any) => any;
export declare const _startProfilerIdleNotifier: (...args: any) => any;
export declare const _stopProfilerIdleNotifier: (...args: any) => any;
export declare const _tickCallback: (...args: any) => any;
export declare const _linkedBinding: (...args: any) => any;
export declare const domain: undefined;
export declare const initgroups: (...args: any) => any;
export declare const moduleLoadList: string[];
export declare const reallyExit: typeof noop;
export declare const _exiting = false;
export declare const _events: never[];
export declare const _eventsCount = 0;
export declare const _maxListeners = 0;
export declare const process: any;
