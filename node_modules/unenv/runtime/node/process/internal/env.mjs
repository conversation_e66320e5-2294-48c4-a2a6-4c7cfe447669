const _envShim = /* @__PURE__ */ Object.create(null);
const _processEnv = globalThis.process?.env;
const _getEnv = (useShim) => _processEnv || globalThis.__env__ || (useShim ? _envShim : globalThis);
export const env = new Proxy(_envShim, {
  get(_, prop) {
    const env2 = _getEnv();
    return env2[prop] ?? _envShim[prop];
  },
  has(_, prop) {
    const env2 = _getEnv();
    return prop in env2 || prop in _envShim;
  },
  set(_, prop, value) {
    const env2 = _getEnv(true);
    env2[prop] = value;
    return true;
  },
  deleteProperty(_, prop) {
    const env2 = _getEnv(true);
    delete env2[prop];
    return true;
  },
  ownKeys() {
    const env2 = _getEnv();
    return Object.keys(env2);
  }
});
