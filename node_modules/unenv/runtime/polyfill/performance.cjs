"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});

var _performance = require("../web/performance/index.cjs");
var _globalThis = _interopRequireDefault(require("./global-this.cjs"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
_globalThis.default.performance = _globalThis.default.performance || _performance.performance;
_globalThis.default.Performance = _globalThis.default.Performance || _performance.Performance;
_globalThis.default.PerformanceEntry = _globalThis.default.PerformanceEntry || _performance.PerformanceEntry;
_globalThis.default.PerformanceMark = _globalThis.default.PerformanceMark || _performance.PerformanceMark;
_globalThis.default.PerformanceMeasure = _globalThis.default.PerformanceMeasure || _performance.PerformanceMeasure;
_globalThis.default.PerformanceObserver = _globalThis.default.PerformanceObserver || _performance.PerformanceObserver;
_globalThis.default.PerformanceObserverEntryList = _globalThis.default.PerformanceObserverEntryList || _performance.PerformanceObserverEntryList;
_globalThis.default.PerformanceResourceTiming = _globalThis.default.PerformanceResourceTiming || _performance.PerformanceResourceTiming;
module.exports = _globalThis.default.performance;