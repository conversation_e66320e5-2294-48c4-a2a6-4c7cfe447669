export declare const URL: {
    new (url: string | URL, base?: string | URL): URL;
    prototype: URL;
    canParse(url: string | URL, base?: string | URL): boolean;
    createObjectURL(obj: Blob | MediaSource): string;
    parse(url: string | URL, base?: string | URL): URL | null;
    revokeObjectURL(url: string): void;
};
export declare const URLSearchParams: {
    new (init?: string[][] | Record<string, string> | string | URLSearchParams): URLSearchParams;
    prototype: URLSearchParams;
};
export declare const parseURL: (...args: any) => any;
export declare const basicURLParse: (...args: any) => any;
export declare const serializeURL: (...args: any) => any;
export declare const serializeHost: (...args: any) => any;
export declare const serializeInteger: (...args: any) => any;
export declare const serializeURLOrigin: (...args: any) => any;
export declare const setTheUsername: (...args: any) => any;
export declare const setThePassword: (...args: any) => any;
export declare const cannotHaveAUsernamePasswordPort: (...args: any) => any;
export declare const percentDecodeBytes: (...args: any) => any;
export declare const percentDecodeString: (...args: any) => any;
