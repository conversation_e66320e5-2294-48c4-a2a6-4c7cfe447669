export const _supportedEntryTypes = [
  "event",
  // PerformanceEntry
  "mark",
  // PerformanceMark
  "measure",
  // PerformanceMeasure
  "resource"
  // PerformanceResourceTiming
];
export class _PerformanceEntry {
  __unenv__ = true;
  detail;
  entryType = "event";
  name;
  startTime;
  constructor(name, options) {
    this.name = name;
    this.startTime = options?.startTime || performance.now();
    this.detail = options?.detail;
  }
  get duration() {
    return performance.now() - this.startTime;
  }
  toJSON() {
    return {
      name: this.name,
      entryType: this.entryType,
      startTime: this.startTime,
      duration: this.duration,
      detail: this.detail
    };
  }
}
export const PerformanceEntry = globalThis.PerformanceEntry || _PerformanceEntry;
export class _PerformanceMark extends _PerformanceEntry {
  entryType = "mark";
}
export const PerformanceMark = globalThis.PerformanceMark || _PerformanceMark;
export class _PerformanceMeasure extends _PerformanceEntry {
  entryType = "measure";
}
export const PerformanceMeasure = globalThis.PerformanceMeasure || _PerformanceMeasure;
export class _PerformanceResourceTiming extends _PerformanceEntry {
  entryType = "resource";
  serverTiming = [];
  connectEnd = 0;
  connectStart = 0;
  decodedBodySize = 0;
  domainLookupEnd = 0;
  domainLookupStart = 0;
  encodedBodySize = 0;
  fetchStart = 0;
  initiatorType = "";
  name = "";
  nextHopProtocol = "";
  redirectEnd = 0;
  redirectStart = 0;
  requestStart = 0;
  responseEnd = 0;
  responseStart = 0;
  secureConnectionStart = 0;
  startTime = 0;
  transferSize = 0;
  workerStart = 0;
  responseStatus = 0;
}
export const PerformanceResourceTiming = globalThis.PerformanceResourceTiming || _PerformanceResourceTiming;
