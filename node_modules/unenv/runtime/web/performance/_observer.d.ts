export declare class _PerformanceObserver implements globalThis.PerformanceObserver {
    readonly __unenv__ = true;
    static supportedEntryTypes: ReadonlyArray<string>;
    _callback: PerformanceObserverCallback | null;
    constructor(callback: PerformanceObserverCallback);
    takeRecords(): PerformanceEntryList;
    disconnect(): void;
    observe(options: PerformanceObserverInit): void;
}
export declare const PerformanceObserver: typeof globalThis.PerformanceObserver;
export declare class _PerformanceObserverEntryList implements globalThis.PerformanceObserverEntryList {
    readonly __unenv__ = true;
    getEntries(): PerformanceEntryList;
    getEntriesByName(_name: string, _type?: string | undefined): PerformanceEntryList;
    getEntriesByType(type: string): PerformanceEntryList;
}
export declare const PerformanceObserverEntryList: typeof globalThis.PerformanceObserverEntryList;
