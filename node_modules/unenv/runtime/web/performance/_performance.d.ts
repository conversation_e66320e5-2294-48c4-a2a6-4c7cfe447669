export declare class _Performance<PerformanceEntryT extends PerformanceEntry = PerformanceEntry> implements globalThis.Performance {
    readonly __unenv__ = true;
    timeOrigin: number;
    eventCounts: EventCounts;
    _entries: PerformanceEntry[];
    _resourceTimingBufferSize: number;
    navigation: any;
    timing: any;
    onresourcetimingbufferfull: ((this: Performance, ev: Event) => any) | null;
    now(): number;
    clearMarks(markName?: string | undefined): void;
    clearMeasures(measureName?: string | undefined): void;
    clearResourceTimings(): void;
    getEntries(): PerformanceEntryT[];
    getEntriesByName(name: string, type?: string | undefined): PerformanceEntryT[];
    getEntriesByType(type: string): PerformanceEntryT[];
    mark(name: string, options?: PerformanceMarkOptions | undefined): PerformanceMark;
    measure(measureName: string, startOrMeasureOptions?: string | PerformanceMeasureOptions, endMark?: string): PerformanceMeasure;
    setResourceTimingBufferSize(maxSize: number): void;
    toJSON(): this;
    addEventListener<K extends "resourcetimingbufferfull">(type: K, listener: (this: Performance, ev: PerformanceEventMap[K]) => any, options?: boolean | AddEventListenerOptions | undefined): void;
    addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions | undefined): void;
    removeEventListener<K extends "resourcetimingbufferfull">(type: K, listener: (this: Performance, ev: PerformanceEventMap[K]) => any, options?: boolean | EventListenerOptions | undefined): void;
    removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions | undefined): void;
    dispatchEvent(event: Event): boolean;
}
export declare const Performance: typeof globalThis.Performance;
export declare const performance: typeof globalThis.performance;
