export declare const _supportedEntryTypes: readonly ["event", "mark", "measure", "resource"];
export type _PerformanceEntryType = (typeof _supportedEntryTypes)[number];
export declare class _PerformanceEntry implements globalThis.PerformanceEntry {
    readonly __unenv__ = true;
    detail: any | undefined;
    entryType: _PerformanceEntryType;
    name: string;
    startTime: number;
    constructor(name: string, options?: PerformanceMarkOptions);
    get duration(): number;
    toJSON(): {
        name: string;
        entryType: "event" | "mark" | "measure" | "resource";
        startTime: number;
        duration: number;
        detail: any;
    };
}
export declare const PerformanceEntry: typeof globalThis.PerformanceEntry;
export declare class _PerformanceMark extends _PerformanceEntry implements globalThis.PerformanceMark {
    entryType: "mark";
}
export declare const PerformanceMark: typeof globalThis.PerformanceMark;
export declare class _PerformanceMeasure extends _PerformanceEntry implements globalThis.PerformanceMeasure {
    entryType: "measure";
}
export declare const PerformanceMeasure: typeof globalThis.PerformanceMeasure;
export declare class _PerformanceResourceTiming extends _PerformanceEntry implements globalThis.PerformanceResourceTiming {
    entryType: "resource";
    serverTiming: readonly PerformanceServerTiming[];
    connectEnd: number;
    connectStart: number;
    decodedBodySize: number;
    domainLookupEnd: number;
    domainLookupStart: number;
    encodedBodySize: number;
    fetchStart: number;
    initiatorType: string;
    name: string;
    nextHopProtocol: string;
    redirectEnd: number;
    redirectStart: number;
    requestStart: number;
    responseEnd: number;
    responseStart: number;
    secureConnectionStart: number;
    startTime: number;
    transferSize: number;
    workerStart: number;
    responseStatus: number;
}
export declare const PerformanceResourceTiming: typeof globalThis.PerformanceResourceTiming;
