"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Performance", {
  enumerable: true,
  get: function () {
    return _performance.Performance;
  }
});
Object.defineProperty(exports, "PerformanceEntry", {
  enumerable: true,
  get: function () {
    return _entry.PerformanceEntry;
  }
});
Object.defineProperty(exports, "PerformanceMark", {
  enumerable: true,
  get: function () {
    return _entry.PerformanceMark;
  }
});
Object.defineProperty(exports, "PerformanceMeasure", {
  enumerable: true,
  get: function () {
    return _entry.PerformanceMeasure;
  }
});
Object.defineProperty(exports, "PerformanceObserver", {
  enumerable: true,
  get: function () {
    return _observer.PerformanceObserver;
  }
});
Object.defineProperty(exports, "PerformanceObserverEntryList", {
  enumerable: true,
  get: function () {
    return _observer.PerformanceObserverEntryList;
  }
});
Object.defineProperty(exports, "PerformanceResourceTiming", {
  enumerable: true,
  get: function () {
    return _entry.PerformanceResourceTiming;
  }
});
Object.defineProperty(exports, "_Performance", {
  enumerable: true,
  get: function () {
    return _performance._Performance;
  }
});
Object.defineProperty(exports, "_PerformanceEntry", {
  enumerable: true,
  get: function () {
    return _entry._PerformanceEntry;
  }
});
Object.defineProperty(exports, "_PerformanceMark", {
  enumerable: true,
  get: function () {
    return _entry._PerformanceMark;
  }
});
Object.defineProperty(exports, "_PerformanceMeasure", {
  enumerable: true,
  get: function () {
    return _entry._PerformanceMeasure;
  }
});
Object.defineProperty(exports, "_PerformanceObserver", {
  enumerable: true,
  get: function () {
    return _observer._PerformanceObserver;
  }
});
Object.defineProperty(exports, "_PerformanceObserverEntryList", {
  enumerable: true,
  get: function () {
    return _observer._PerformanceObserverEntryList;
  }
});
Object.defineProperty(exports, "_PerformanceResourceTiming", {
  enumerable: true,
  get: function () {
    return _entry._PerformanceResourceTiming;
  }
});
Object.defineProperty(exports, "performance", {
  enumerable: true,
  get: function () {
    return _performance.performance;
  }
});
var _performance = require("./_performance.cjs");
var _observer = require("./_observer.cjs");
var _entry = require("./_entry.cjs");