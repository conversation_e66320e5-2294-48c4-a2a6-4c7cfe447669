import { createNotImplementedError } from "../../_internal/utils.mjs";
import { _supportedEntryTypes } from "./_entry.mjs";
export class _PerformanceObserver {
  __unenv__ = true;
  static supportedEntryTypes = _supportedEntryTypes;
  _callback = null;
  constructor(callback) {
    this._callback = callback;
  }
  takeRecords() {
    return [];
  }
  disconnect() {
    throw createNotImplementedError("PerformanceObserver.disconnect");
  }
  observe(options) {
    throw createNotImplementedError("PerformanceObserver.observe");
  }
}
export const PerformanceObserver = globalThis.PerformanceObserver || _PerformanceObserver;
export class _PerformanceObserverEntryList {
  __unenv__ = true;
  getEntries() {
    return [];
  }
  getEntriesByName(_name, _type) {
    return [];
  }
  getEntriesByType(type) {
    return [];
  }
}
export const PerformanceObserverEntryList = globalThis.PerformanceObserverEntryList || _PerformanceObserverEntryList;
